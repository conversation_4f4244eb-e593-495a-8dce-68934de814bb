variable "WUNDERGRAPH" {
  type = string
}

locals {
  namespace = "production"
  name = "lp-graph-production"
  routing_url = "https://graphql.luxurypresence.com/graphql"
  label_matchers = ["env=production", "federated=production"]
}

resource "cosmo_namespace" "namespace" {
  name = local.namespace
}

resource "cosmo_federated_graph" "graph" {
  name           = local.name
  routing_url    = local.routing_url
  namespace      = local.namespace
  label_matchers = local.label_matchers
  depends_on = [cosmo_namespace.namespace]
}