variable "WUNDERGRAPH" {
  type = string
}

locals {
  namespace = "staging"
  name = "lp-graph-staging"
  routing_url = "https://graphql.luxurycoders.com/graphql"
  label_matchers = ["env=staging", "federated=staging"]
}

resource "cosmo_namespace" "namespace" {
  name = local.namespace
}

resource "cosmo_federated_graph" "graph" {
  name           = local.name
  routing_url    = local.routing_url
  namespace      = local.namespace
  label_matchers = local.label_matchers
  depends_on = [cosmo_namespace.namespace]
}