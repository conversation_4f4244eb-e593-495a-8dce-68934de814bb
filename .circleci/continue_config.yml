version: 2.1

executors:
  default-executor:
    docker:
      - image: luxurypresence/alpine-curl:v0.0.1
        auth:
          username: $DOCKER_LOGIN
          password: $DOCKER_PASSWORD
orbs:
  aws-cli: circleci/aws-cli@4.1.2
  node: circleci/node@5.2.0
parameters:
  # PRODUCTION parameters
  prod-us-east-1-vpc1:
    type: boolean
    default: false
  prod-us-east-1-iam-roles-service-accounts:
    type: boolean
    default: false
  #-----------------------------------------------------------------------------------------------------------------
  # STAGING parameters
  staging-us-east-1-vpc1:
    type: boolean
    default: false
  staging-us-east-1-eks1-karpenter:
    type: boolean
    default: false
  staging-us-east-1-subdomain1:
    type: boolean
    default: false
  staging-us-east-1-logdna-cloudwatch1:
    type: boolean
    default: false
  staging-us-east-1-transit-connect1:
    type: boolean
    default: false
  staging-us-east-1-redis1:
    type: boolean
    default: false
  staging-us-east-1-alb-ingress-controller-policies:
    type: boolean
    default: false
  staging-us-east-1-iam-roles-service-accounts:
    type: boolean
    default: false
  staging-us-east-1-database1:
    type: boolean
    default: false
  #-----------------------------------------------------------------------------------------------------------------
  # OPERATIONS parameters
  operations-us-east-1-elasticsearch1:
    type: boolean
    default: false
  #-----------------------------------------------------------------------------------------------------------------
  # Manual parameters
  manual-prod-us-east-1-vpc1:
    type: boolean
    default: false
  manual-prod-us-east-1-iam-roles-service-accounts:
    type: boolean
    default: false
  manual-staging-us-east-1-vpc1:
    type: boolean
    default: false
  manual-staging-us-east-1-eks1-karpenter:
    type: boolean
    default: false
  manual-staging-us-east-1-subdomain1:
    type: boolean
    default: false
  manual-staging-us-east-1-logdna-cloudwatch1:
    type: boolean
    default: false
  manual-staging-us-east-1-transit-connect1:
    type: boolean
    default: false
  manual-staging-us-east-1-redis1:
    type: boolean
    default: false
  manual-staging-us-east-1-alb-ingress-controller-policies:
    type: boolean
    default: false
  manual-staging-us-east-1-iam-roles-service-accounts:
    type: boolean
    default: false
  manual-staging-us-east-1-database1:
    type: boolean
    default: false
  manual-operations-us-east-1-elasticsearch1:
    type: boolean
    default: false

commands:
  terraform_common:
    parameters:
      tf_path:
        type: string
      cci_role_arn:
        type: string
    steps:
      - checkout
      - aws-cli/setup:
          profile_name: default
          role_arn: << parameters.cci_role_arn >>
      - run:
          name: Download and Install Terraform
          command: |
            TERRAFORM_VERSION="1.7.5"  # Replace with the desired Terraform version
            curl -o terraform.zip https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip
            unzip terraform.zip
            mv terraform /usr/local/bin/
            terraform --version
            ssh-keyscan github.com >> ~/.ssh/known_hosts
      - run:
          name: Terraform Init
          command: |
            cd << parameters.tf_path >>
            eval `ssh-agent -s`
            ssh-add ~/.ssh/id_ed25519
            terraform init
      - run:
          name: Terraform Plan
          command: |
            pwd
            cd << parameters.tf_path >>
            terraform plan

jobs:
  install-code:
    parameters:
      node-version:
        type: string
        default: '16.13'
      path:
        type: string
        default: 'no-path'
      package-name:
        type: string
        default: 'no-name'
      env:
        type: string
        default: 'no-env'
      arn:
        type: string
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - node/install:
          node-version: << parameters.node-version >>
      - aws-cli/setup:
          profile_name: default
          role_arn: << parameters.arn >>
          region: us-east-1
      - run:
          command: |
            cd << parameters.path >>
            npm install
            zip -r << parameters.package-name >>.zip .
            aws s3 cp << parameters.package-name >>.zip s3://lp-lambda-serverless-deploy-<< parameters.env >>/infrastructure/<< parameters.package-name >>.zip

  setup-terraform:
    executor: default-executor
    resource_class: small
    parameters:
      plan_path:
        type: string
      arn:
        type: string
    steps:
      - terraform_common:
          tf_path: << parameters.plan_path >>
          cci_role_arn: << parameters.arn >>

  terraform-apply:
    executor: default-executor
    resource_class: medium
    parameters:
      apply_path:
        type: string
      arn:
        type: string
    steps:
      - terraform_common:
          tf_path: << parameters.apply_path >>
          cci_role_arn: << parameters.arn >>
      - run:
          name: Terraform Apply
          command: |
            cd << parameters.apply_path >>
            terraform plan
            terraform apply -auto-approve

  terraform-destroy:
    executor: default-executor
    resource_class: medium
    parameters:
      apply_path:
        type: string
      arn:
        type: string
    steps:
      - terraform_common:
          tf_path: << parameters.apply_path >>
          cci_role_arn: << parameters.arn >>
      - run:
          name: Terraform Destroy
          command: |
            cd << parameters.apply_path >>
            terraform plan
            terraform destroy -auto-approve

workflows:
  # PRODUCTION workflows
  # Prod us-east-1 VPC1
  terraform-prod-us-east-1-vpc1:
    when:
      or:
        - << pipeline.parameters.prod-us-east-1-vpc1 >>
        - << pipeline.parameters.manual-prod-us-east-1-vpc1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/prod/us-east-1/vpc1"
          arn: "$PROD_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/prod/us-east-1/vpc1"
          arn: "$PROD_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
  # Prod us-east-1 iam-roles-service-accounts
  terraform-prod-us-east-1-iam-roles-service-accounts:
    when:
      or:
        - << pipeline.parameters.prod-us-east-1-iam-roles-service-accounts >>
        - << pipeline.parameters.manual-prod-us-east-1-iam-roles-service-accounts >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/prod/us-east-1/iam-roles-service-accounts"
          arn: "$PROD_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/prod/us-east-1/iam-roles-service-accounts"
          arn: "$PROD_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
  #-----------------------------------------------------------------------------------------------------------------
  # STAGING WORKFLOWS
  # Staging us-east-1 VPC1
  terraform-staging-us-east-1-vpc1:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-vpc1 >>
        - << pipeline.parameters.manual-staging-us-east-1-vpc1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/vpc1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/vpc1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
  # Staging us-east-1 EKS1 with Karpenter
  terraform-staging-us-east-1-eks1-karpenter:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-eks1-karpenter >>
        - << pipeline.parameters.manual-staging-us-east-1-eks1-karpenter >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/eks1_karpenter"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/eks1_karpenter"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
      - destroy_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-destroy:
          apply_path: "bootstrap/staging/us-east-1/eks1_karpenter"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - destroy_approval
  # Staging us-east-1 subdomain1
  terraform-staging-us-east-1-subdomain1:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-subdomain1 >>
        - << pipeline.parameters.manual-staging-us-east-1-subdomain1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/subdomain1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/subdomain1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
  # Staging us-east-1 logdna-cloudwatch1
  terraform-staging-us-east-1-logdna-cloudwatch1:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-logdna-cloudwatch1 >>
        - << pipeline.parameters.manual-staging-us-east-1-logdna-cloudwatch1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/logdna-cloudwatch1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - install-code:
          path: "bootstrap/staging/us-east-1/logdna-cloudwatch1/code"
          package-name: "logdna-cloudwatch1"
          env: "staging"
          arn: "$STAGING_CCI_ARN"
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/logdna-cloudwatch1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - install-code
  # Staging us-east-1 subdomain1
  terraform-staging-us-east-1-transit-connect1:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-transit-connect1 >>
        - << pipeline.parameters.manual-staging-us-east-1-transit-connect1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/transit-connect1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/transit-connect1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
  # Staging us-east-1 redis1
  terraform-staging-us-east-1-redis1:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-redis1 >>
        - << pipeline.parameters.manual-staging-us-east-1-redis1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/redis1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/redis1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
  # Staging us-east-1 alb-ingress-controller-policies
  terraform-staging-us-east-1-alb-ingress-controller-policies:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-alb-ingress-controller-policies >>
        - << pipeline.parameters.manual-staging-us-east-1-alb-ingress-controller-policies >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/alb-ingress-controller-policies"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/alb-ingress-controller-policies"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
      - destroy_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-destroy:
          apply_path: "bootstrap/staging/us-east-1/alb-ingress-controller-policies"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - destroy_approval
  # Staging us-east-1 iam-roles-service-accounts
  terraform-staging-us-east-1-iam-roles-service-accounts:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-iam-roles-service-accounts >>
        - << pipeline.parameters.manual-staging-us-east-1-iam-roles-service-accounts >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/iam-roles-service-accounts"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/iam-roles-service-accounts"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
      - destroy_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-destroy:
          apply_path: "bootstrap/staging/us-east-1/iam-roles-service-accounts"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - destroy_approval
  # Staging us-east-1 database1
  terraform-staging-us-east-1-database1:
    when:
      or:
        - << pipeline.parameters.staging-us-east-1-database1 >>
        - << pipeline.parameters.manual-staging-us-east-1-database1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/staging/us-east-1/database1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/staging/us-east-1/database1"
          arn: "$STAGING_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
  #-----------------------------------------------------------------------------------------------------------------
  # OPERATIONS workflows
  # Operations us-east-1 Elasticsearch1
  terraform-operations-us-east-1-elasticsearch1:
    when:
      or:
        - << pipeline.parameters.operations-us-east-1-elasticsearch1 >>
        - << pipeline.parameters.manual-operations-us-east-1-elasticsearch1 >>
    jobs:
      - setup-terraform:
          plan_path: "bootstrap/operations/us-east-1/elasticsearch1"
          arn: "$OPERATIONS_CCI_ARN"
          context: dockerhub
      - apply_approval:
          type: approval
          filters:
            branches:
              only:
                - main
          requires:
            - setup-terraform
      - terraform-apply:
          apply_path: "bootstrap/operations/us-east-1/elasticsearch1"
          arn: "$OPERATIONS_CCI_ARN"
          context: dockerhub
          filters:
            branches:
              only:
                - main
          requires:
            - apply_approval
