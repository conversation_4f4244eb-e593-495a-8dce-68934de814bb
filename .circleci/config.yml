version: 2.1
setup: true

orbs:
  path-filtering: circleci/path-filtering@1.0.0
  continuation: circleci/continuation@1.0.0

parameters:
  manual-prod-us-east-1-vpc1:
    type: boolean
    default: false
  manual-prod-us-east-1-iam-roles-service-accounts:
    type: boolean
    default: false
  manual-staging-us-east-1-vpc1:
    type: boolean
    default: false
  manual-staging-us-east-1-eks1-karpenter:
    type: boolean
    default: false
  manual-staging-us-east-1-subdomain1:
    type: boolean
    default: false
  manual-staging-us-east-1-logdna-cloudwatch1:
    type: boolean
    default: false
  manual-staging-us-east-1-transit-connect1:
    type: boolean
    default: false
  manual-staging-us-east-1-redis1:
    type: boolean
    default: false
  manual-staging-us-east-1-alb-ingress-controller-policies:
    type: boolean
    default: false
  manual-staging-us-east-1-iam-roles-service-accounts:
    type: boolean
    default: false
  manual-staging-us-east-1-database1:
    type: boolean
    default: false
  manual-operations-us-east-1-elasticsearch1:
    type: boolean
    default: false

# workflows:
  # setup:
  #   unless:
  #     or:
  #       - << pipeline.parameters.manual-prod-us-east-1-vpc1 >>
  #       - << pipeline.parameters.manual-prod-us-east-1-iam-roles-service-accounts >>
  #       - << pipeline.parameters.manual-staging-us-east-1-vpc1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-eks1-karpenter >>
  #       - << pipeline.parameters.manual-staging-us-east-1-subdomain1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-logdna-cloudwatch1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-transit-connect1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-redis1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-alb-ingress-controller-policies >>
  #       - << pipeline.parameters.manual-staging-us-east-1-iam-roles-service-accounts >>
  #       - << pipeline.parameters.manual-staging-us-east-1-database1 >>
  #       - << pipeline.parameters.manual-operations-us-east-1-elasticsearch1 >>
  #   jobs:
  #     - path-filtering/filter:
  #         name: check-updated-files
  #         mapping: |
  #           bootstrap/prod/us-east-1/vpc1/.* prod-us-east-1-vpc1 true
  #           bootstrap/prod/us-east-1/iam-roles-service-accounts/.* prod-us-east-1-iam-roles-service-accounts true
  #           bootstrap/staging/us-east-1/vpc1/.* staging-us-east-1-vpc1 true
  #           bootstrap/staging/us-east-1/eks1_karpenter/.* staging-us-east-1-eks1-karpenter true
  #           bootstrap/staging/us-east-1/subdomain1/.* staging-us-east-1-subdomain1 true
  #           bootstrap/staging/us-east-1/logdna-cloudwatch1/.* staging-us-east-1-logdna-cloudwatch1 true
  #           bootstrap/staging/us-east-1/transit-connect1/.* staging-us-east-1-transit-connect1 true
  #           bootstrap/staging/us-east-1/redis1/.* staging-us-east-1-redis1 true
  #           bootstrap/staging/us-east-1/alb-ingress-controller-policies/.* staging-us-east-1-alb-ingress-controller-policies true
  #           bootstrap/staging/us-east-1/iam-roles-service-accounts/.* staging-us-east-1-iam-roles-service-accounts true
  #           bootstrap/staging/us-east-1/database1/.* staging-us-east-1-database1 true
  #           bootstrap/operations/us-east-1/elasticsearch1/.* operations-us-east-1-elasticsearch1 true
  #         config-path: .circleci/continue_config.yml
  # setup-manual:
  #   when:
  #     or:
  #       - << pipeline.parameters.manual-prod-us-east-1-vpc1 >>
  #       - << pipeline.parameters.manual-prod-us-east-1-iam-roles-service-accounts >>
  #       - << pipeline.parameters.manual-staging-us-east-1-vpc1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-eks1-karpenter >>
  #       - << pipeline.parameters.manual-staging-us-east-1-subdomain1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-logdna-cloudwatch1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-transit-connect1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-redis1 >>
  #       - << pipeline.parameters.manual-staging-us-east-1-alb-ingress-controller-policies >>
  #       - << pipeline.parameters.manual-staging-us-east-1-iam-roles-service-accounts >>
  #       - << pipeline.parameters.manual-staging-us-east-1-database1 >>
  #       - << pipeline.parameters.manual-operations-us-east-1-elasticsearch1 >>
  #   jobs:
  #     - continuation/continue:
  #         configuration_path: .circleci/continue_config.yml
  #         parameters: /tmp/pipeline-parameters.json
  #         pre-steps:
  #           - run:
  #               command: |
  #                 echo '{
  #                   "prod-us-east-1-vpc1": << pipeline.parameters.manual-prod-us-east-1-vpc1 >>,
  #                   "prod-us-east-1-iam-roles-service-accounts": << pipeline.parameters.manual-prod-us-east-1-iam-roles-service-accounts >>,
  #                   "staging-us-east-1-vpc1": << pipeline.parameters.manual-staging-us-east-1-vpc1 >>,
  #                   "staging-us-east-1-eks1-karpenter": << pipeline.parameters.manual-staging-us-east-1-eks1-karpenter >>,
  #                   "staging-us-east-1-subdomain1": << pipeline.parameters.manual-staging-us-east-1-subdomain1 >>,
  #                   "staging-us-east-1-logdna-cloudwatch1": << pipeline.parameters.manual-staging-us-east-1-logdna-cloudwatch1 >>,
  #                   "staging-us-east-1-transit-connect1": << pipeline.parameters.manual-staging-us-east-1-transit-connect1 >>,
  #                   "staging-us-east-1-redis1": << pipeline.parameters.manual-staging-us-east-1-redis1 >>,
  #                   "staging-us-east-1-alb-ingress-controller-policies": << pipeline.parameters.manual-staging-us-east-1-alb-ingress-controller-policies >>,
  #                   "staging-us-east-1-iam-roles-service-accounts": << pipeline.parameters.manual-staging-us-east-1-iam-roles-service-accounts >>,
  #                   "staging-us-east-1-database1": << pipeline.parameters.manual-staging-us-east-1-database1 >>,
  #                   "operations-us-east-1-elasticsearch1": << pipeline.parameters.manual-operations-us-east-1-elasticsearch1 >>
  #                 }' >> /tmp/pipeline-parameters.json

jobs:
  disabled-pipeline:
    docker:
      - image: circleci/python:3.7
    steps:
      - run:
          name: Pipeline Disabled
          command: echo "This pipeline was temporarily disabled."

workflows:
  version: 2
  disabled-pipeline:
    jobs:
      - disabled-pipeline
