# Import the default environment
data "confluent_environment" "default" {
  id = "env-w156km"
}

# AWS VPC
data "terraform_remote_state" "vpc1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "use1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "aws_caller_identity" "current" {
  provider = aws.current_account
}

data "aws_caller_identity" "bastion" {
  provider = aws.bastion_account
}

data "terraform_remote_state" "shared_vars" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/shared-vars/terraform.tfstate"
    region = "us-east-1"
  }
}

data "aws_route_table" "bastion_public_route_table" {
  provider = aws.bastion_account
  vpc_id = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.id
  filter {
    name   = "tag:Type"
    values = ["public"]
  }
}