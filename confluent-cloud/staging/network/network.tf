module "vpc_peering" {
  source      = "**************:luxurypresence/tf-modules.git//confluent-cloud/vpc-peering?ref=main&depth=1"

  providers = {
    aws.current_account = aws.current_account
    aws.bastion_account = aws.bastion_account
  }

  environment = "staging"
  confluent_cloud_region = "us-east-1"
  confluent_cloud_api_key = var.confluent_cloud_api_key
  confluent_cloud_api_secret = var.confluent_cloud_api_secret
  confluent_cloud_environment_id = data.confluent_environment.default.id
  confluent_network_cidr = "10.31.0.0/16"
  confluent_network_zone_info = [
    {
      cidr = "10.31.0.0/27"
      zone_id = "use1-az4"
    },
    {
      cidr = "10.31.0.32/27"
      zone_id = "use1-az6"
    },
    {
      cidr = "10.31.0.64/27"
      zone_id = "use1-az2"
    }
  ]
  # staging vpc
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_vpc_id = data.terraform_remote_state.vpc1.outputs.vpc_id
  aws_vpc_cidr_block = data.terraform_remote_state.vpc1.outputs.vpc_cidr_block
  aws_route_table_ids = data.terraform_remote_state.vpc1.outputs.private_route_table_ids
  aws_private_network_acl_id = data.terraform_remote_state.vpc1.outputs.private_network_acl_id

  # bastion vpc
  aws_bastion_account_id = data.aws_caller_identity.bastion.account_id
  aws_bastion_vpc_id = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.id
  aws_bastion_vpc_cidr_block = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr

  aws_bastion_route_table_ids = [data.aws_route_table.bastion_public_route_table.id]
}