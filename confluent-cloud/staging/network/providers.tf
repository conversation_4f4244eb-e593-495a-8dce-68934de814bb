terraform {
  backend "s3" {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/confluent-cloud/network/terraform.tfstate"
    region = "us-east-1"
  }
  required_providers {
    confluent = {
      source  = "confluentinc/confluent"
      version = "2.34.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "6.4.0"
    }
  }
}

provider "aws" {
  region = "us-east-1"
  alias = "current_account"
  assume_role {
    role_arn = "arn:aws:iam::************:role/atlantis-access-staging"
  }
}

provider "aws" {
  region = "us-east-1"
  alias = "bastion_account"
  assume_role {
    role_arn = "arn:aws:iam::************:role/atlantis-access-production"
  }
}

provider "confluent" {
  cloud_api_key    = var.confluent_cloud_api_key
  cloud_api_secret = var.confluent_cloud_api_secret
}