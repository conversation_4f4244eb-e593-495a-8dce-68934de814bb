resource "confluent_kafka_cluster" "staging_1" {
  display_name = "lp_staging_cluster"
  availability = "SINGLE_ZONE"
  cloud        = "AWS"
  region       = "us-east-1"
  dedicated {
    cku = 1
  }

  environment {
    id = data.confluent_environment.default.id
  }

  network {
    id = data.terraform_remote_state.confluent_cloud_network.outputs.network_id
  }

  lifecycle {
    prevent_destroy = true
  }
}
