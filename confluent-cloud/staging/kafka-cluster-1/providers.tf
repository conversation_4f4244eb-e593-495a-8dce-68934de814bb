terraform {
  backend "s3" {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/confluent-cloud/kafka-cluster-1/terraform.tfstate"
    region = "us-east-1"
  }
  required_providers {
    confluent = {
      source  = "confluentinc/confluent"
      version = "2.34.0"
    }
  }
}

provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn = "arn:aws:iam::093949242303:role/atlantis-access-staging"
  }
}

provider "confluent" {
  cloud_api_key    = var.confluent_cloud_api_key
  cloud_api_secret = var.confluent_cloud_api_secret
}