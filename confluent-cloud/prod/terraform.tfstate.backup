{"version": 4, "terraform_version": "1.8.5", "serial": 4, "lineage": "934fa541-428d-b75a-51cc-04967d6ad1a9", "outputs": {}, "resources": [{"mode": "managed", "type": "confluent_environment", "name": "production", "provider": "provider[\"registry.terraform.io/confluentinc/confluent\"]", "instances": [{"schema_version": 0, "attributes": {"display_name": "Production", "id": "env-jvr87q", "resource_name": "crn://confluent.cloud/organization=07add302-5472-47b8-9817-6bb99bf7cde3/environment=env-jvr87q", "stream_governance": [{"package": "ESSENTIALS"}]}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "confluent_kafka_cluster", "name": "production", "provider": "provider[\"registry.terraform.io/confluentinc/confluent\"]", "instances": [{"schema_version": 1, "attributes": {"api_version": "cmk/v2", "availability": "SINGLE_ZONE", "basic": [], "bootstrap_endpoint": "SASL_SSL://pkc-p11xm.us-east-1.aws.confluent.cloud:9092", "byok_key": [{"id": ""}], "cloud": "AWS", "dedicated": [], "display_name": "lp_production_cluster", "enterprise": [], "environment": [{"id": "env-jvr87q"}], "freight": [], "id": "lkc-o5p67j", "kind": "Cluster", "network": [{"id": ""}], "rbac_crn": "crn://confluent.cloud/organization=07add302-5472-47b8-9817-6bb99bf7cde3/environment=env-jvr87q/cloud-cluster=lkc-o5p67j", "region": "us-east-1", "rest_endpoint": "https://pkc-p11xm.us-east-1.aws.confluent.cloud:443", "standard": [{}], "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoyNTkyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MjU5MjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["confluent_environment.production"]}]}], "check_results": null}