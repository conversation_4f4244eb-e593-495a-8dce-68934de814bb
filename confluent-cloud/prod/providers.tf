terraform {
  backend "s3" {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/confluent-cloud/production/terraform.tfstate"
    region = "us-east-1"
    role_arn = "arn:aws:iam::381475384502:role/atlantis-access-production"
  }
  required_providers {
    confluent = {
      source  = "confluentinc/confluent"
      version = "1.77.0"
    }
  }
}

provider "aws" {
  alias  = "target"
  region = "us-east-1"
  assume_role {
    role_arn = "arn:aws:iam::381475384502:role/atlantis-access-production"
  }
}

provider "confluent" {
  cloud_api_key    = var.confluent_cloud_api_key
  cloud_api_secret = var.confluent_cloud_api_secret
}


provider "aws" {
  region = "us-east-1"
}
