locals {
  private_subnets = data.terraform_remote_state.staging_vpc1.outputs.private_subnet_ids
  vpc_cidr_block = data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block
  vpc_id = data.terraform_remote_state.staging_vpc1.outputs.vpc_id
  private_route_table_ids = data.terraform_remote_state.staging_vpc1.outputs.private_route_table_ids
}

data "terraform_remote_state" "staging_vpc1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "use1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

module "endpoints" {
  source = "terraform-aws-modules/vpc/aws//modules/vpc-endpoints"
  version = "~> 5.13"

  vpc_id             = local.vpc_id
  create_security_group = true
  security_group_name = "vpc-endpoint-sg"
  security_group_rules = [
    {
      type        = "ingress"
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr_blocks = [local.vpc_cidr_block]
    },
    {
      type        = "egress"
      from_port   = 0
      to_port     = 0
      protocol    = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
    }
  ]

  endpoints = {
    s3 = {
      service             = "s3"
      service_type        = "Gateway"
      route_table_ids     = local.private_route_table_ids
      tags                = { Name = "s3-vpc-endpoint" }
    },
    sqs = {
      service             = "sqs"
      service_type        = "Interface"
      private_dns_enabled = true
      subnet_ids          = local.private_subnets
      tags                = { Name = "sqs-vpc-endpoint" }
    },
    monitoring = {
      service             = "monitoring"
      service_type        = "Interface"
      private_dns_enabled = true
      subnet_ids          = local.private_subnets
      tags                = { Name = "monitoring-vpc-endpoint" }
    },
    logs = {
      service             = "logs"
      service_type        = "Interface"
      private_dns_enabled = true
      subnet_ids          = local.private_subnets
      tags                = { Name = "logs-vpc-endpoint" }
    },
    airflow-api = {
      service             = "airflow.api"
      service_type        = "Interface"
      private_dns_enabled = true
      subnet_ids          = local.private_subnets
      tags                = { Name = "airflow-api-vpc-endpoint" }
    },
    airflow-env = {
      service             = "airflow.env"
      service_type        = "Interface"
      private_dns_enabled = true
      subnet_ids          = local.private_subnets
      tags                = { Name = "airflow-env-vpc-endpoint" }
    },
    airflow-ops = {
      service             = "airflow.ops"
      service_type        = "Interface"
      private_dns_enabled = true
      subnet_ids          = local.private_subnets
      tags                = { Name = "airflow-ops-vpc-endpoint" }
    },
  }

  tags = {
    Terraform = "true"
  }
}
