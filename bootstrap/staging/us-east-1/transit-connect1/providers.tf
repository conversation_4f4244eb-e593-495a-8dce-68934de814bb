terraform {
  backend "s3" {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/transit-connect1/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = "us-east-1"
  alias = "current_account"
  assume_role {
    role_arn = "arn:aws:iam::************:role/atlantis-access-staging"
  }
}

provider "aws" {
  region = "us-east-1"
  alias = "root_account"
  assume_role {
    role_arn     = "arn:aws:iam::************:role/atlantis-access-production"
  }
}
