locals {
  vpc_id                   = data.terraform_remote_state.staging_vpc1.outputs.vpc_id
}

module "transit_connect" {
  source             = "**************:luxurypresence/tf-modules.git//transit-connect?ref=main&depth=1"
  providers = {
    aws = aws.current_account
    aws.current_account = aws.current_account
    aws.root_account = aws.root_account
  }
  vpc_id = local.vpc_id
  install_transit_gw_routes_by_subnet_type = [
    "private-vpc1",
    "public-vpc1"
  ]
  allowed_traffic_by_subnet_type = {
    "public-vpc1": [
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
    ],
    "private-vpc1": [
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
      data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.staging.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.production.cidr,
    ]
  }
  subnet_type_priority = ["private-vpc1"]
  transit_gw_id = "tgw-053ab671f43b42f2c"
  tgw_in_same_aws_account = false
}
