locals {
  vpc_id                   = data.terraform_remote_state.staging_vpc1.outputs.vpc_id
  private_subnet_ids       = data.terraform_remote_state.staging_vpc1.outputs.private_subnet_ids
}

/*
module "search_cluster" {
  source = "**************:luxurypresence/tf-modules.git//elasticsearch?ref=main"
  name           = "clickstream"
  cluster_name   = "clickstream"
  environment    = "staging"
  instance_class = "r5.large.elasticsearch"
  engine = {
    engine_version = "7.10"
  }
  vpc = {
    vpc_id                         = local.vpc_id
    cluster_subnets                = slice(local.private_subnet_ids, 0, 2)
    allowed_cidr_blocks = [
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
      data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block,
    ]
  }
  enable_advanced_security   = false
  enabled_internal_master    = false
  allocated_storage          = 30
  volume_throughput          = 125
  ebs_iops                   = 3000
  az_count                   = 2
  instance_count             = 2
  custom_domain              = "clickstream-search-cluster.luxurycoders.com"
  custom_domain_tls_cert_arn = "arn:aws:acm:us-east-1:093949242303:certificate/b494bdee-8bc3-4f93-bfca-919b568dada1"
  tags = {
    env  = "staging"
    team = "data-kraken"
    app  = "clickstream"
    caller-2 = "kubernetes"
  }
}
*/
