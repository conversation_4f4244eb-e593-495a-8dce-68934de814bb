data "aws_vpc" "staging_vpc" {
  filter {
    name   = "tag:peering"
    values = ["staging_vpc1"]
  }
}

data "aws_vpc" "eks_vpc" {
  filter {
    name   = "tag:peering"
    values = ["staging-legacy-vpc"]
  }
}


module "staging_vpc_peering1" {
  source = "**************:luxurypresence/tf-modules.git//vpc-peering?ref=main&depth=1"
  providers = {
    aws.src = aws
    aws.dst = aws
  }

  source_vpc_id              = data.aws_vpc.staging_vpc.id
  destination_vpc_id         = data.aws_vpc.eks_vpc.id
  dns_resolution             = true
}
