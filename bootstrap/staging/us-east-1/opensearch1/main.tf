locals {
  vpc_id                   = data.terraform_remote_state.staging_vpc1.outputs.vpc_id
  private_subnet_ids       = data.terraform_remote_state.staging_vpc1.outputs.private_subnet_ids
}

module "search_cluster" {
  source = "**************:luxurypresence/tf-modules.git//opensearch?ref=v1.0.0"
  name           = "search-service"
  cluster_name   = "search-service-cluster"
  environment    = "staging"
  instance_class = "m7g.medium.search"
  engine = {
    engine_version = "OpenSearch_2.17"
  }
  vpc = {
    vpc_id                         = local.vpc_id
    cluster_subnets                = slice(local.private_subnet_ids, 0, 3)
    allowed_cidr_blocks = [
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
      data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block,
    ]
  }

  enable_advanced_security   = true
  enabled_internal_master    = true
  allocated_storage          = 50
  ebs_throughput             = 250
  ebs_iops                   = 7000
  az_count                   = 3
  instance_count             = 3
  master_enabled             = false
  master_count               = 0
  master_type                = "m7g.medium.search"
  custom_domain              = "search-service-opensearch-cluster.luxurycoders.com"
  custom_domain_tls_cert_arn = "arn:aws:acm:us-east-1:093949242303:certificate/b494bdee-8bc3-4f93-bfca-919b568dada1"
  create_manual_snapshot_resources = true
  tags = {
    env  = "staging"
    team = "maps"
    app  = "search-service"
  }
}

