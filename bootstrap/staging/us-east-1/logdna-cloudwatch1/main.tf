data "aws_iam_policy_document" "lambda_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_policy_attachment" "lambda-basic-execution-attach" {
  name       = "logdna-cloudwatch1-attach"
  roles      = [aws_iam_role.logdna.name]
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_policy_attachment" "cloudwatch-full-access-attach" {
  name       = "logdna-cloudwatch1-attach"
  roles      = [aws_iam_role.logdna.name]
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchFullAccess"
}

resource "aws_iam_role" "logdna" {
  name               = "logdna-cloudwatch1"
  assume_role_policy = data.aws_iam_policy_document.lambda_assume_role.json
  tags = {
    Terraform = "true"
    Environment = "staging"
    team = "swat"
  }
}

resource "aws_lambda_function" "logdna" {
  function_name = "logdna-cloudwatch1"
  role          = aws_iam_role.logdna.arn
  handler       = "index.handler"

  s3_bucket = var.bucket_upload
  s3_key    = "infrastructure/logdna-cloudwatch1.zip"

  runtime = "nodejs18.x"

  environment {
    variables = {
      LOGDNA_INGESTION_KEY = var.logdna_ingestion_key
      LOGDNA_TAGS         = var.logdna_tags
    }
  }

  tags = {
    Terraform = "true"
    Environment = "staging"
    team = "swat"
  }
}

resource "aws_lambda_permission" "logdna" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.logdna.function_name
  principal     = "logs.amazonaws.com"
  source_arn    = "arn:aws:logs:us-east-1:093949242303:log-group:/aws/lambda/*"
}
