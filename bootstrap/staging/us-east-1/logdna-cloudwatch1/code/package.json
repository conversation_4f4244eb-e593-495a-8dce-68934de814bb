{"name": "logdna-cloudwatch", "version": "2.2.1", "description": "Lambda Functions to Stream Logs from AWS CloudWatch to LogDNA", "main": "index.js", "scripts": {"lint": "./node_modules/.bin/eslint -c .eslintrc index.js", "test": "tap"}, "dependencies": {"agentkeepalive": "^4.0.2", "async": "^2.6.2", "request": "^2.88.0"}, "devDependencies": {"eslint": "^6.7.2", "tap": "^14.10.7"}, "keywords": ["lambda", "logdna", "aws", "cloudwatch"], "repository": {"type": "git", "url": "https://github.com/logdna/logdna-cloudwatch.git"}, "author": "LogDNA", "license": "MIT", "bugs": {"url": "https://github.com/logdna/logdna-cloudwatch/issues"}, "homepage": "https://github.com/logdna/logdna-cloudwatch"}