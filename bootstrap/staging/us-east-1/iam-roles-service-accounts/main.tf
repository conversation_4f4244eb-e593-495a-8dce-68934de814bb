module "crossplane-provider-aws" {
  source = "**************:luxurypresence/tf-modules.git//iam/irsa?ref=main&depth=1"

  role_name = "crossplane-provider-aws-role"
  policy_name = "crossplane-provider-aws-policy"
  environment = "staging"
  cluster_oidc_provider_arn = "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/0B0E16CE9DBD3BAA2DD34C6C2CA3543E"
  namespace_service_accounts = [
    "crossplane-system:provider-aws-*",
  ]
  policy_manifest_file = "./policies/crossplane-provider-aws.json"
  condition_operator = "StringLike"
}
