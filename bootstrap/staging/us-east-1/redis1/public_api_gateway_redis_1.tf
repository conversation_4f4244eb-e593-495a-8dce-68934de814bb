module "public_api_gateway_redis_1" {
  source = "**************:luxurypresence/tf-modules.git//redis?ref=main"
  environment    = "staging"
  cluster_name = "public-api-gateway-redis-1"
  name = "public-api-gateway-redis-1"
  port = 6379
  engine = {
    engine_version = "7.0"
    family         = "redis7"
  }
  instance_class = "cache.t3.small"
  automatic_failover_enabled = false
  multi_az_enabled = false
  tags = {
    app              = "public-api-gateway-redis-1"
  }
  number_of_shards = 1
  replicas_per_node_group = 0
  vpc = {
    vpc_id                         = local.vpc_id
    cluster_subnets                = local.private_subnet_ids
    allowed_cidr_blocks = [
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
      data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block,
    ]
  }
  maintenance = {
    maintenance_window        = "Sun:11:00-Sun:12:00"
    snapshot_window           = ""
    snapshot_retention_period = 0
  }
}
