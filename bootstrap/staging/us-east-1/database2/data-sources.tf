data "terraform_remote_state" "networking_self" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "use1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "shared_vars" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/shared-vars/terraform.tfstate"
    region = "us-east-1"
  }
}

data "aws_caller_identity" "current" {}

data "aws_iam_session_context" "example" {
  arn = data.aws_caller_identity.current.arn
}

# confluent cloud data sources
data "terraform_remote_state" "confluent_cloud_staging_network" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/confluent-cloud/network/terraform.tfstate"
    region = "us-east-1"
  }
}