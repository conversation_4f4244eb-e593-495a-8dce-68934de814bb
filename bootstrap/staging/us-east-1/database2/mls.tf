module "mls_database" {
  source      = "**************:luxurypresence/tf-modules.git//rds-aurora?ref=main&depth=1"
  name        = "mls-cluster-2"
  environment = "staging"
  username    = "luxurypresence82"
  port        = 5432
  subnet_type = "private-vpc1"
  enable_performance_insights = true
  master_user_secret_policy_principals = local.master_user_secret_policy_principals
  iam_database_authentication_enabled = true
  vpc = {
    vpc_id                         = local.vpc_id
    allowed_self_vpc_subnets_types = ["private-vpc1", "public-vpc1"] # Allow to communicate to DB from private and public subnets in the same VPC
    allowed_external_cidr_blocks = concat([
        "**********/16", # Legacy VPC
        "**********/16",  # Ops VPC - needed by Vault
        "**********/16", # Bastion VPC
        "********/16",  # Old Staging VPC
        "*********/16", # Production VPC
      ],
      data.terraform_remote_state.confluent_cloud_staging_network.outputs.confluent_cluster_zone_info_ips
    )
  }
  security_group_egress_rules = {
    to_cidrs = {
      cidr_blocks = ["0.0.0.0/0"]
    }
  }
  engine = {
    engine         = "aurora-postgresql"
    engine_version = "16"
    family         = "aurora-postgresql16"
  }
  instances = {
    1 : {
      instance_class : "db.r6g.large"
    },
    2 : {
      instance_class : "db.r6g.large"
    }
  }
  cluster_parameters = [
    {
      name  = "log_connections"
      value = "1"
    },
    {
      name         = "shared_preload_libraries"
      value        = "pg_stat_statements,pglogical"
      apply_method = "pending-reboot"
    },
    {
      name         = "rds.logical_replication"
      value        = 1
      apply_method = "pending-reboot"
    },
    {
      name         = "max_replication_slots"
      value        = 10
      apply_method = "pending-reboot"
    },
    {
      name         = "max_wal_senders"
      value        = 15
      apply_method = "pending-reboot"
    }
  ]
}
