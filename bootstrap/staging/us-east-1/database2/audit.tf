module "audit_database_1" {
  source      = "**************:luxurypresence/tf-modules.git//rds-aurora?ref=main&depth=1"
  name        = "audit-cluster-2"
  environment = "staging"
  username    = "luxurypresence82"
  port        = 5432
  subnet_type = "private-vpc1"
  enable_performance_insights = true
  master_user_secret_policy_principals = local.master_user_secret_policy_principals
  iam_database_authentication_enabled = true
  vpc = {
    vpc_id                         = local.vpc_id
    allowed_self_vpc_subnets_types = ["private-vpc1", "public-vpc1"] # Allow to communicate to DB from private subnets in the same VPC
    allowed_external_cidr_blocks = concat([
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
      data.terraform_remote_state.shared_vars.outputs.staging.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.production.cidr,
    ],
    data.terraform_remote_state.confluent_cloud_staging_network.outputs.confluent_cluster_zone_info_ips
    )
  }
  security_group_egress_rules = {
    to_cidrs = {
      cidr_blocks = ["0.0.0.0/0"]
    }
  }
  engine = {
    engine         = "aurora-postgresql"
    engine_version = "16"
    family         = "aurora-postgresql16"
  }
  instances = {
    1 : {
      instance_class : "db.r6g.xlarge"
    }
  }
  cluster_parameters = [
    {
      name  = "log_connections"
      value = "1"
    },
    {
      name  = "rds.logical_replication"
      value = "1",
      apply_method = "pending-reboot"
    },
    {
      name  = "shared_preload_libraries"
      value = "pg_stat_statements,pglogical"
      apply_method = "pending-reboot"
    },
    {
      name  = "max_replication_slots"
      value = "30"
      apply_method = "pending-reboot"
    },
    {
      name = "max_worker_processes",
      value = "30"
      apply_method = "pending-reboot"
    },
    {
      name = "max_wal_senders",
      value = "30"
      apply_method = "pending-reboot"
    }
  ]
}
