resource "aws_s3_bucket" "lp_data_dsa_file_storage_etl_staging" {
  bucket = "lp-data-dsa-file-storage-etl-staging"

  tags = {
    Terraform = "true"
    Environment = "staging"
    Owner = "dsa-team"
  }
}

resource "aws_s3_bucket_policy" "lp_data_dsa_file_storage_etl_staging_policy" {
  bucket = aws_s3_bucket.lp_data_dsa_file_storage_etl_staging.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowSandboxAccess"
        Effect    = "Allow"
        Principal = {
          AWS = "arn:aws:iam::825569692836:root"
        }
        Action = [
          "s3:*"
        ]
        Resource = [
          aws_s3_bucket.lp_data_dsa_file_storage_etl_staging.arn,
          "${aws_s3_bucket.lp_data_dsa_file_storage_etl_staging.arn}/*"
        ]
      },
      {
        Sid = "AllowSnowflakeAccessFromProduction"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::381475384502:role/SnowflakeAccess"
        }
        Action = [
          "s3:*"
        ]
        Resource = [
          aws_s3_bucket.lp_data_dsa_file_storage_etl_staging.arn,
          "${aws_s3_bucket.lp_data_dsa_file_storage_etl_staging.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket" "launchx_gong_transcripts" {
  bucket = "launchx-gong-transcripts-staging"

  tags = {
    Terraform = "true"
    Environment = "staging"
    Owner = "dsa-team"
  }
}
