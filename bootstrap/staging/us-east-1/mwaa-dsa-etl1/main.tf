data "terraform_remote_state" "emr1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/emr1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "mwaa_shared1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "us-east-1/mwaa-shared1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "vpc1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-staging"
    key    = "use1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

module "mwaa" {
  source = "**************:luxurypresence/tf-modules.git//mwaa-cluster?ref=main&depth=1"
  environment                           = "staging"
  cluster_name                          = "staging-dsa-etl1"
  airflow_version                       = "2.9.2"
  environment_class                     = "mw1.medium"
  min_workers                           = 1
  max_workers                           = 10
  dag_s3_path                           = "dags/"
  plugins_s3_path                       = "plugins/plugins.zip"
  requirements_s3_path                  = "requirements.txt"
  schedulers                            = 5
  source_bucket_prefix                  = "lp-data-mwaa"
  webserver_access_mode                 = "PUBLIC_ONLY"
  athena_data_access_buckets = [
    "arn:aws:s3:::lp-data-airflow-metadata-staging",
    "arn:aws:s3:::lp-data-airflow-metadata-staging/*",
    "arn:aws:s3:::lp-data-dsa-file-storage-etl-staging",
    "arn:aws:s3:::lp-data-dsa-file-storage-etl-staging/*",
    "arn:aws:s3:::lp-staging-posthog-data",
    "arn:aws:s3:::lp-staging-posthog-data/*"
  ]
  athena_query_results_buckets = [
    "arn:aws:s3:::aws-athena-query-results-*-us-east-1",
    "arn:aws:s3:::aws-athena-query-results-*-us-east-1/*"
  ]
  airflow_configuration_options = {
    "webserver.web_server_master_timeout" = 180
    "webserver.web_server_worker_timeout" = 180
    "metrics.metrics_allow_list"          = "none"
    "webserver.expose_config"             = "True"
    "core.dag_file_processor_timeout"           = 600
    "core.dagbag_import_timeout"                = 300
    "core.min_serialized_dag_update_interval"   = 600
    "scheduler.min_file_process_interval"       = 600
    "scheduler.schedule_after_task_execution"   = false
  }

  network_configuration = {
    vpc_id             = data.terraform_remote_state.vpc1.outputs.vpc_id
    security_group_ids = []
    subnet_ids = [
      data.terraform_remote_state.vpc1.outputs.private_subnet_ids[0],
      data.terraform_remote_state.vpc1.outputs.private_subnet_ids[1]
    ]
  }

  logging_configuration = {
    dag_processing_logs = {
      enable    = true
      log_level = "INFO"
    }
    scheduler_logs = {
      enable    = true
      log_level = "INFO"
    }
    task_logs = {
      enable    = true
      log_level = "INFO"
    }
    webserver_logs = {
      enable    = true
      log_level = "INFO"
    }
    worker_logs = {
      enable    = true
      log_level = "INFO"
    }
  }

  emr_configuration = {
    iam_role_arns = [data.terraform_remote_state.emr1.outputs.emr_iam_role_arn, data.terraform_remote_state.emr1.outputs.emr_ec2_iam_role_arn]
  }

  data_sync_updates_topic_arn = data.terraform_remote_state.mwaa_shared1.outputs.data_sync_updates_topic_arn
  tags = {
    "Cluster" = "staging-dsa-etl1"
  }
}
