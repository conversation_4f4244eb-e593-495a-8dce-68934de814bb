module "emr" {
  source = "**************:luxurypresence/tf-modules.git//emr?ref=main&depth=1"
  environment_name        = "staging"
  emr_bucket_prefix      = "lp-data-emr"
  emr_access_bucket_arns = [
    "arn:aws:s3:::lp-data-emr-staging",
    "arn:aws:s3:::lp-data-emr-staging/*",
    "arn:aws:s3:::lp-datalakehouse-stage",
    "arn:aws:s3:::lp-datalakehouse-stage/*",
    "arn:aws:s3:::lp-data-mwaa-staging",
    "arn:aws:s3:::lp-data-mwaa-staging/*",
    "arn:aws:s3:::qa-extract-s3-bucket-stage",
    "arn:aws:s3:::qa-extract-s3-bucket-stage/*",
    "arn:aws:s3:::qwr-terraform-state-staging",
    "arn:aws:s3:::qwr-terraform-state-staging/*",
    "arn:aws:s3:::lp-staging-posthog-data",
    "arn:aws:s3:::lp-staging-posthog-data/*",
    "arn:aws:s3:::etl-slug-*",
    "arn:aws:s3:::etl-slug-*/*"
  ]
  network_configuration = {
    vpc_id     = data.terraform_remote_state.staging_vpc1.outputs.vpc_id
    cidr_block = data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block
  }
  additional_security_group_rules = [
    {
      type        = "ingress"
      from_port   = -1
      to_port     = -1
      protocol    = -1
      cidr_blocks = [data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr]
    },
    {
      type        = "ingress"
      from_port   = -1
      to_port     = -1
      protocol    = -1
      cidr_blocks = [data.terraform_remote_state.shared_vars.outputs.staging.vpc_config.legacy.cidr]
    }
  ]
  tags = {
    Cluster = "staging"
  }
}
