locals {
  ami      = data.aws_ssm_parameter.al2023_ami.value
  key_name = "podifi-bastion"
}

resource "aws_security_group" "podifi" {
   name        = "podifi-bastion-sg"
  description = "podifi bastion security group"
  vpc_id      = data.terraform_remote_state.staging_vpc1.outputs.vpc_id

  tags = {
    Name = "podifi sg"
  }
}

resource "aws_security_group_rule" "ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.podifi.id
  description       = "Allow SSH inbound traffic"
}

resource "aws_security_group_rule" "allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.podifi.id
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_instance" "podifi" {
  ami                         = local.ami
  instance_type               = "t3.nano"
  subnet_id                   = data.terraform_remote_state.staging_vpc1.outputs.public_subnet_ids[0]
  associate_public_ip_address = true
  vpc_security_group_ids      = [aws_security_group.podifi.id]
  key_name                    = local.key_name

  root_block_device {
    volume_size = 15
    volume_type = "gp3"
  }

  tags = {
    Name = "podifi bastion"
  }
}

resource "aws_eip" "podifi_eip" {
  instance = aws_instance.podifi.id
  domain   = "vpc"
  tags = {
    Name = "podifi bastion"
  }
}
