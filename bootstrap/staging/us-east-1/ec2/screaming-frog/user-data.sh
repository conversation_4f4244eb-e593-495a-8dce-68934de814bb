#!/bin/bash

# Update package lists
apt-get update

# Install required packages
apt-get install -y \
    unzip \
    snapd

# Install AWS SSM Agent
snap install amazon-ssm-agent --classic

# Start and enable SSM agent
systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service
systemctl enable snap.amazon-ssm-agent.amazon-ssm-agent.service

# Verify SSM agent is running
systemctl status snap.amazon-ssm-agent.amazon-ssm-agent.service
