resource "aws_instance" "sfrog" {
  ami                         = local.ami
  instance_type               = "t3.xlarge"
  user_data                   = file("${path.module}/user-data.sh")
  subnet_id                   = local.subnet_id
  associate_public_ip_address = true
  iam_instance_profile        = aws_iam_instance_profile.sfrog_profile.name
  vpc_security_group_ids      = [aws_security_group.sfrog_sg.id]

  root_block_device {
    volume_size = 100
    volume_type = "gp3"
  }

  tags = {
    Name = "screaming frog poc"
  }
}

resource "aws_security_group" "sfrog_sg" {
  name        = "sfrog-sg"
  description = "screaming frog security group"
  vpc_id      = local.vpc_id

  tags = {
    Name = "screaming frog sg"
  }
}

resource "aws_security_group_rule" "ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = var.make_cidr_blocks
  security_group_id = aws_security_group.sfrog_sg.id
  description       = "Allow SSH inbound traffic from specified make.com IPs"
}

resource "aws_security_group_rule" "allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.sfrog_sg.id
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "http" {
  type              = "ingress"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.sfrog_sg.id
  description       = "Allow HTTP inbound traffic"
}

resource "aws_security_group_rule" "https" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.sfrog_sg.id
  description       = "Allow HTTPS inbound traffic"
}

resource "aws_iam_instance_profile" "sfrog_profile" {
  name = "screaming_frog_profile"
  role = aws_iam_role.sfrog.name
}

resource "aws_iam_role" "sfrog" {
  name = "screaming_frog_staging_role"
  path = "/"

  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Action": "sts:AssumeRole",
            "Principal": {
               "Service": "ec2.amazonaws.com"
            },
            "Effect": "Allow",
            "Sid": ""
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "sfrog_ssm-core-attach" {
  role       = aws_iam_role.sfrog.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}
