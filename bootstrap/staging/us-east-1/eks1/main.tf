locals {
  cluster_name = "staging_eks1"
}

module "iam_role_eks_access_devs" {
  source = "**************:luxurypresence/tf-modules.git//iam/eks_access?ref=main&depth=1"
  environment        = "staging"
  cluster_name       = local.cluster_name
  role_name          = "${local.cluster_name}-devs-access"
  policy_name        = "${local.cluster_name}-devs-access"
  account_id = data.aws_caller_identity.current.account_id
}

module "staging_eks1" {
  source = "**************:luxurypresence/tf-modules.git//eks_karpenter?ref=main&depth=1"
  cluster_name       = local.cluster_name
  environment        = "staging"
  owner              = "platformeng"
  vpc_id             = data.terraform_remote_state.staging_vpc1.outputs.vpc_id
  private_subnet_ids = data.terraform_remote_state.staging_vpc1.outputs.private_subnet_ids
  control_plane_subnet_ids  = data.terraform_remote_state.staging_vpc1.outputs.public_subnet_ids
  subnet_selector_tag_value = "private-vpc1"
  cluster_version   = "1.31"
  cluster_endpoint_public_access = true
  ssh_cidr_blocks   = [
    data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block,
    data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
  ]
  control_plane_cidr_blocks = [
    data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block,
    data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
    data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.cidr,
    data.terraform_remote_state.shared_vars.outputs.staging.vpc_config.legacy.cidr,
  ]
  additional_tags    = {
    team = "platformeng"
  }
  kms_key_administrators = [
    "arn:aws:iam::************:role/OrganizationAccountAccessRole",
    "arn:aws:iam::************:role/circleci-terraform-admin",
    "arn:aws:iam::************:role/atlantis-access-staging",
  ]
  authentication_mode = "API_AND_CONFIG_MAP"
  access_entries = {
    OrganizationAccountAccessRole = {
      principal_arn = "arn:aws:iam::************:role/OrganizationAccountAccessRole"
      user_name = "OrganizationAccountAccessRole"

      policy_associations = {
        AmazonEKSClusterAdminPolicy = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
          access_scope = {
            type       = "cluster"
          }
        }
      }
    },
    AtlantisOperationsRole = {
      principal_arn = "arn:aws:iam::************:role/atlantis-role"
      user_name = "AtlantisOperationsRole"

      policy_associations = {
        AmazonEKSClusterAdminPolicy = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
          access_scope = {
            type       = "cluster"
          }
        }
      }
    },
    AtlantisStagingRole = {
      principal_arn = "arn:aws:iam::************:role/atlantis-access-staging"
      user_name = "AtlantisStagingRole"

      policy_associations = {
        AmazonEKSClusterAdminPolicy = {
          policy_arn = "arn:aws:eks::aws:cluster-access-policy/AmazonEKSClusterAdminPolicy"
          access_scope = {
            type       = "cluster"
          }
        }
      }
    },
  }
  additional_aws_auth_roles = [
    {
      rolearn = "arn:aws:iam::************:role/cast-staging_eks1-eks-3c6c4101"
      username = "system:node:{{EC2PrivateDNSName}}"
      groups   = ["system:bootstrappers","system:nodes"]
    },
    {
      rolearn = module.iam_role_eks_access_devs.role_arn
      username = "devs-role"
      groups   = ["lp:developers:base"]
    },
    {
      rolearn = "arn:aws:iam::************:role/staging1-mmaa-executor"
      username = "mwaa-service"
      groups   = ["lp:mwaa"]
    },
    {
      rolearn = "arn:aws:iam::************:role/staging-mmaa-executor"
      username = "mwaa-service"
      groups   = ["lp:mwaa"]
    },
  ]
  cluster_addons = {
    coredns = {
      addon_version = "v1.11.4-eksbuild.14"
      configuration_values = jsonencode(
        {
          "tolerations": [
            {
              key: "node-type",
              operator: "Equal",
              value: "on-demand",
              effect: "NoSchedule"
            },
          ],
        }
      )
    }
    kube-proxy = {
      addon_version = "v1.31.7-eksbuild.7"
    }
    vpc-cni = {
      addon_version = "v1.19.5-eksbuild.3"
    }
    aws-ebs-csi-driver = {
      addon_version = "v1.44.0-eksbuild.1"
      configuration_values = jsonencode(
        {
          "controller": {
            "tolerations": [
              {
                key: "node-type",
                operator: "Equal",
                value: "on-demand",
                effect: "NoSchedule"
              },
            ],
          }
        }
      )
      service_account_role_arn = module.aws_ebs_csi_driver_role.role_arn
    }
  }
}

# IAM Role for EBS CSI Driver addon
module "aws_ebs_csi_driver_role" {
  source = "**************:luxurypresence/tf-modules.git//iam/irsa?ref=main&depth=1"

  role_name = "aws-ebs-csi-driver-role"
  policy_name = "aws-ebs-csi-driver-policy"
  environment = "staging"
  cluster_oidc_provider_arn = "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/0B0E16CE9DBD3BAA2DD34C6C2CA3543E"
  namespace_service_accounts = [
    "kube-system:ebs-csi-*",
  ]
  managed_policy_arns = [
    "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy",
  ]
  condition_operator = "StringLike"
}

resource "aws_security_group" "lambda_vpc_access" {
  name        = "lambda-vpc-access"
  description = "Allow all Lambda functions to access VPC"
  vpc_id      = data.terraform_remote_state.staging_vpc1.outputs.vpc_id
}

resource "aws_vpc_security_group_ingress_rule" "allow_all_traffic_ipv4" {
  security_group_id = aws_security_group.lambda_vpc_access.id
  cidr_ipv4   = data.terraform_remote_state.staging_vpc1.outputs.vpc_cidr_block
  from_port   = 0
  to_port     = 0
  ip_protocol = "tcp"
}

resource "aws_vpc_security_group_egress_rule" "allow_all_traffic_ipv4" {
  security_group_id = aws_security_group.lambda_vpc_access.id
  cidr_ipv4         = "0.0.0.0/0"
  ip_protocol       = "-1"
}

resource "aws_security_group_rule" "sealed_secrets" {
  description = "Allow Sealed Secrets to access cluster control plane"
  type        = "ingress"
  from_port   = 8080
  to_port     = 8080
  protocol    = "tcp"
  source_security_group_id = module.staging_eks1.cluster_security_group_id
  security_group_id = module.staging_eks1.node_group_security_group_id
}
