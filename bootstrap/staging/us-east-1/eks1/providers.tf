# No interpolations allowed in Backend configurations
terraform {
  backend "s3" {
    bucket = "lp-tf-state-staging"
    key    = "use1/eks1_karpenter/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn = "arn:aws:iam::093949242303:role/atlantis-access-staging"
  }
}

provider "kubernetes" {
  host                   = module.staging_eks1.cluster_endpoint
  cluster_ca_certificate = base64decode(module.staging_eks1.cluster_certificate_authority_data)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args = ["eks", "get-token", "--cluster-name", module.staging_eks1.cluster_name, "--region", "us-east-1"]
  }
}

provider "helm" {
  kubernetes {
    host                   = module.staging_eks1.cluster_endpoint
    cluster_ca_certificate = base64decode(module.staging_eks1.cluster_certificate_authority_data)
    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args = ["eks", "get-token", "--cluster-name", module.staging_eks1.cluster_name, "--region", "us-east-1"]
    }
  }
}

provider "kubectl" {
  host                   = module.staging_eks1.cluster_endpoint
  cluster_ca_certificate = base64decode(module.staging_eks1.cluster_certificate_authority_data)
  load_config_file       = false
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args = ["eks", "get-token", "--cluster-name", module.staging_eks1.cluster_name, "--region", "us-east-1"]
  }
}
