resource "aws_s3_bucket" "launchx_gong_transcripts" {
  bucket = "launchx-gong-transcripts-production"

  tags = {
    Terraform = "true"
    Environment = "production"
    Owner = "dsa-team"
  }
}

resource "aws_s3_bucket" "lp_data_dsa_file_storage_etl_production" {
  bucket = "lp-data-dsa-file-storage-etl-production"

  tags = {
    Terraform = "true"
    Environment = "production"
    Owner = "dsa-team"
  }
}

# temporary policy for sandbox access until we create the production cluster
resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = aws_s3_bucket.launchx_gong_transcripts.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowSandboxAccess"
        Effect    = "Allow"
        Principal = {
          AWS = "arn:aws:iam::825569692836:root"
        }
        Action = [
          "s3:*"
        ]
        Resource = [
          aws_s3_bucket.launchx_gong_transcripts.arn,
          "${aws_s3_bucket.launchx_gong_transcripts.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_s3_bucket_policy" "lp_data_dsa_file_storage_etl_production_policy" {
  bucket = aws_s3_bucket.lp_data_dsa_file_storage_etl_production.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowSandboxAccess"
        Effect    = "Allow"
        Principal = {
          AWS = "arn:aws:iam::825569692836:root"
        }
        Action = [
          "s3:*"
        ]
        Resource = [
          aws_s3_bucket.lp_data_dsa_file_storage_etl_production.arn,
          "${aws_s3_bucket.lp_data_dsa_file_storage_etl_production.arn}/*"
        ]
      },
      {
        Sid       = "AllowPendoAccess"
        Effect    = "Allow"
        Principal = {
          AWS =  [
              "arn:aws:iam::381475384502:role/pendo_datasync",
              "arn:aws:iam::545571961360:user/pendo-datasync"
          ]
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:GetBucketLocation"
        ]
        Resource = [
          aws_s3_bucket.lp_data_dsa_file_storage_etl_production.arn,
          "${aws_s3_bucket.lp_data_dsa_file_storage_etl_production.arn}/pendo/*"
        ]
      }
    ]
  })
}