# No interpolations allowed in Backend configurations
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "s3" {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/security-hub/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn = "arn:aws:iam::381475384502:role/atlantis-access-production"
  }
}
