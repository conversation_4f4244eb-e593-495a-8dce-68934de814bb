
# S3 bucket to store the findings
resource "aws_s3_bucket" "security_findings_bucket" {
  bucket = "lp-security-hub-findings-bucket"
}

resource "aws_s3_bucket_ownership_controls" "security_findings_bucket_ownership" {
  bucket = aws_s3_bucket.security_findings_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "security_findings_bucket_acl" {
  depends_on = [aws_s3_bucket_ownership_controls.security_findings_bucket_ownership]
  bucket     = aws_s3_bucket.security_findings_bucket.id
  acl        = "private"
}

# IAM role for Firehose to write to S3
resource "aws_iam_role" "firehose_role" {
  name = "firehose_security_hub_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "firehose.amazonaws.com"
        }
      }
    ]
  })
}

# IAM policy for Firehose to write to S3
resource "aws_iam_policy" "firehose_policy" {
  name        = "firehose_security_hub_policy"
  description = "Policy for Firehose to write Security Hub findings to S3"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "s3:AbortMultipartUpload",
          "s3:GetBucketLocation",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:ListBucketMultipartUploads",
          "s3:PutObject"
        ]
        Effect   = "Allow"
        Resource = [
          aws_s3_bucket.security_findings_bucket.arn,
          "${aws_s3_bucket.security_findings_bucket.arn}/*"
        ]
      },
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

# Attach policy to the role
resource "aws_iam_role_policy_attachment" "firehose_policy_attachment" {
  role       = aws_iam_role.firehose_role.name
  policy_arn = aws_iam_policy.firehose_policy.arn
}

# IAM role for EventBridge to put events to Firehose
resource "aws_iam_role" "eventbridge_role" {
  name = "eventbridge_security_hub_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "events.amazonaws.com"
        }
      }
    ]
  })
}

# IAM policy for EventBridge to put events to Firehose
resource "aws_iam_policy" "eventbridge_policy" {
  name        = "eventbridge_security_hub_policy"
  description = "Policy for EventBridge to send Security Hub findings to Firehose"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "firehose:PutRecord",
          "firehose:PutRecordBatch"
        ]
        Effect   = "Allow"
        Resource = aws_kinesis_firehose_delivery_stream.security_hub_firehose.arn
      }
    ]
  })
}

# Attach policy to the role
resource "aws_iam_role_policy_attachment" "eventbridge_policy_attachment" {
  role       = aws_iam_role.eventbridge_role.name
  policy_arn = aws_iam_policy.eventbridge_policy.arn
}

# Kinesis Firehose Delivery Stream to S3
resource "aws_kinesis_firehose_delivery_stream" "security_hub_firehose" {
  name        = "security-hub-firehose"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn           = aws_iam_role.firehose_role.arn
    bucket_arn         = aws_s3_bucket.security_findings_bucket.arn
    prefix             = "security-hub-findings/json/"
    compression_format = "UNCOMPRESSED"
    buffering_size     = 5  # Size in MB
    buffering_interval = 300  # Time in seconds
  }
}

# EventBridge rule for Security Hub findings
resource "aws_cloudwatch_event_rule" "security_hub_findings_rule" {
  name        = "security-hub-critical-high-findings"
  description = "Capture critical and high severity findings from Security Hub"

  event_pattern = jsonencode({
    source      = ["aws.securityhub"]
    detail-type = ["Security Hub Findings - Imported"]
    detail      = {
      findings = {
        Severity = {
          Label = ["CRITICAL", "HIGH"]
        }
      }
    }
  })
}

# EventBridge target to send findings to Firehose
resource "aws_cloudwatch_event_target" "security_hub_firehose_target" {
  rule      = aws_cloudwatch_event_rule.security_hub_findings_rule.name
  arn       = aws_kinesis_firehose_delivery_stream.security_hub_firehose.arn
  role_arn  = aws_iam_role.eventbridge_role.arn
}
