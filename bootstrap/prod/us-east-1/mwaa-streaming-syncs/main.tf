data "terraform_remote_state" "emr1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/emr1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "mwaa_shared1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/mwaa-shared1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "vpc1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

locals {
  cluster_name = "production-streaming-syncs"
}

module "mwaa" {
  source = "**************:luxurypresence/tf-modules.git//mwaa-cluster?ref=main&depth=1"
  environment                           = "production"
  cluster_name                          = local.cluster_name
  airflow_version                       = "2.9.2"
  environment_class                     = "mw1.xlarge"
  min_workers                           = 4
  max_workers                           = 20
  dag_s3_path                           = "dags/"
  plugins_s3_path                       = "plugins/plugins.zip"
  requirements_s3_path                  = "requirements.txt"
  schedulers                            = 4
  source_bucket_prefix                  = "lp-data-mwaa"
  webserver_access_mode                 = "PUBLIC_ONLY"
  athena_data_access_buckets = [
    "arn:aws:s3:::lp-data-airflow-metadata-production",
    "arn:aws:s3:::lp-data-airflow-metadata-production/*"
  ]
  athena_query_results_buckets = [
    "arn:aws:s3:::aws-athena-query-results-*-us-east-1",
    "arn:aws:s3:::aws-athena-query-results-*-us-east-1/*",
    "arn:aws:s3:::data-etl-service-prod",
    "arn:aws:s3:::data-etl-service-prod/*"
  ]
  airflow_configuration_options = {
    "celery.worker_autoscale"                 = "30,0"
    "core.dag_file_processor_timeout"         = 600
    "core.dagbag_import_timeout"              = 300
    "core.min_serialized_dag_update_interval" = 600
    "metrics.metrics_allow_list"              = "executor,pool,dagrun,scheduler"
    "scheduler.min_file_process_interval"     = 600
    "scheduler.schedule_after_task_execution" = "false"
    "webserver.expose_config"                 = "True"
    "webserver.web_server_master_timeout"     = 180
    "webserver.web_server_worker_timeout"     = 180
  }

  network_configuration = {
    vpc_id             = data.terraform_remote_state.vpc1.outputs.vpc_id
    security_group_ids = []
    subnet_ids = [
      data.terraform_remote_state.vpc1.outputs.private_subnet_ids[0],
      data.terraform_remote_state.vpc1.outputs.private_subnet_ids[1]
    ]
  }

  logging_configuration = {
    dag_processing_logs = {
      enable    = true
      log_level = "ERROR"
    }
    scheduler_logs = {
      enable    = true
      log_level = "INFO"
    }
    task_logs = {
      enable    = true
      log_level = "INFO"
    }
    webserver_logs = {
      enable    = true
      log_level = "ERROR"
    }
    worker_logs = {
      enable    = true
      log_level = "INFO"
    }
  }

  emr_configuration = {
    iam_role_arns = [data.terraform_remote_state.emr1.outputs.emr_iam_role_arn, data.terraform_remote_state.emr1.outputs.emr_ec2_iam_role_arn]
  }

  data_sync_updates_topic_arn = data.terraform_remote_state.mwaa_shared1.outputs.data_sync_updates_topic_arn
  tags = {
    "Cluster" = local.cluster_name
  }
}
