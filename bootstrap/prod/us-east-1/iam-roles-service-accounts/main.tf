module "crossplane-provider-aws-staging-eks1" {
  source = "**************:luxurypresence/tf-modules.git//iam/irsa?ref=main&depth=1"

  role_name = "staging-eks1-crossplane-provider-aws-role"
  policy_name = "staging-eks1-crossplane-provider-aws-policy"
  environment = "staging"
  cluster_oidc_provider_arn = "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/0B0E16CE9DBD3BAA2DD34C6C2CA3543E"
  namespace_service_accounts = [
    "crossplane-system:provider-aws-*",
  ]
  policy_manifest_file = "./policies/crossplane-provider-aws.json"
  condition_operator = "StringLike"
}

module "crossplane-provider-aws-production-eks1" {
  source = "**************:luxurypresence/tf-modules.git//iam/irsa?ref=main&depth=1"

  role_name = "production-eks1-crossplane-provider-aws-role"
  policy_name = "production-eks1-crossplane-provider-aws-policy"
  environment = "production"
  cluster_oidc_provider_arn = "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/2C8B96F9570D9DE958C717D6477E797A"
  namespace_service_accounts = [
    "crossplane-system:provider-aws-*",
  ]
  policy_manifest_file = "./policies/crossplane-provider-aws.json"
  condition_operator = "StringLike"
}
