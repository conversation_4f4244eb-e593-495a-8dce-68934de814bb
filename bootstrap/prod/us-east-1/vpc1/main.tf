module "prod_vpc1" {
  source = "**************:luxurypresence/tf-modules.git//vpc?ref=main&depth=1"
  aws_region = "us-east-1"
  vpc_name = "production_vpc1"
  vpc_cidr = "10.20.0.0/16"
  public_subnet_cidrs = ["10.20.0.0/18", "10.20.64.0/19", "10.20.96.0/19"]
  additional_vpc_tags = {
    "peering" = "production_vpc1"
  }
  additional_public_subnet_tags = {
    Type = "public-vpc1"
    "kubernetes.io/role/elb" = 1
    "kubernetes.io/cluster/production_eks1" = "shared"
  }
  private_subnet_cidrs = ["10.20.128.0/18", "10.20.192.0/19", "10.20.224.0/19"]
  additional_private_subnet_tags = {
    Type = "private-vpc1"
    "kubernetes.io/role/internal-elb" = 1
    "kubernetes.io/cluster/production_eks1" = "shared"
  }
  availability_zones = ["us-east-1a", "us-east-1b", "us-east-1c"]
  owner = "infrastructure"
  create_api_gateway_endpoint = false
  allowed_cidr_blocks = [
    "172.31.0.0/16", # Legacy VPC
    "172.16.0.0/16", # Bastion VPC
  ]
}
