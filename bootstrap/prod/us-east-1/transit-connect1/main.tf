data "terraform_remote_state" "production_vpc1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

locals {
  vpc_id                   = data.terraform_remote_state.production_vpc1.outputs.vpc_id
}

module "transit_connect" {
  source             = "**************:luxurypresence/tf-modules.git//transit-connect?ref=main&depth=1"
  providers = {
    aws = aws.current_account
    aws.current_account = aws.current_account
    aws.root_account = aws.root_account
  }
  vpc_id = local.vpc_id
  install_transit_gw_routes_by_subnet_type = [
    "private-vpc1",
    "public-vpc1"
  ]
  allowed_traffic_by_subnet_type = {
     "public-vpc1": [
      "172.16.0.0/16", # Bastion VPC - access for VPN
    ],
    "private-vpc1": [
      "172.16.0.0/16", # Bastion VPC - access for VPN
      "10.254.0.0/16", # Ops VPC - access for vault
      "172.31.0.0/16",  # Legacy VPC
      "10.3.0.0/16",  # EKS prod Legacy
      "10.21.0.0/16", # Staging VPC
    ]
  }
  subnet_type_priority = ["private-vpc1"]
  transit_gw_id = "tgw-053ab671f43b42f2c"
  tgw_in_same_aws_account = true

}
