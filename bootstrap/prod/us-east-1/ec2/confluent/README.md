Project to create a proxy for the confluent cluster.

Run ansible playbook to install haproxy and configure it to proxy to the confluent cluster.

for production there is VPN connection you are going to need to add you public IP in security group in AWS.
```
python3 -m venv ansible
source ansible/bin/activate
pip install ansible
ansible-playbook haproxy.yaml -i inventory --private-key ~/.ssh/lp-us-east.pem -u ec2-user
```

Domain name: confluent-proxy.luxurypresence.com

```