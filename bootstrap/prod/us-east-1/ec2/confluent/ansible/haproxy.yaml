---
- hosts: servers
  become: yes
  tasks:
    - name: Install HAProxy
      yum:
        name: haproxy
        state: present

    - name: Configure HAProxy
      template:
        src: haproxy.cfg.j2
        dest: /etc/haproxy/haproxy.cfg
      notify:
        - restart haproxy

    - name: Start and enable HAProxy
      service:
        name: haproxy
        state: started
        enabled: true

  handlers:
    - name: restart haproxy
      service:
        name: haproxy
        state: restarted
