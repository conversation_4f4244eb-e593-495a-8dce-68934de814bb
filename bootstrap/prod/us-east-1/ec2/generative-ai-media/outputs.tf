output "instance_id" {
  description = "ID of the ComfyUI EC2 instance"
  value       = aws_instance.gen-ai-media.id
}

output "elastic_ip" {
  description = "Elastic IP address of the ComfyUI instance"
  value       = aws_eip.gen-ai-media_eip.public_ip
}

output "comfyui_url" {
  description = "URL to access ComfyUI web interface"
  value       = "http://${aws_eip.gen-ai-media_eip.public_ip}:8188"
}

output "ssh_command" {
  description = "SSH command to connect to the instance"
  value       = "ssh -i ~/.ssh/${local.key_name}.pem ubuntu@${aws_eip.gen-ai-media_eip.public_ip}"
}
