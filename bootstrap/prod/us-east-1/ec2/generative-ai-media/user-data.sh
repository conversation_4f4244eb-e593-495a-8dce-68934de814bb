#!/bin/bash

# ComfyUI Installation Script for Ubuntu on g5.2xlarge
# This script installs ComfyUI with CUDA support for NVIDIA GPUs

set -e  # Exit on any error

# Create log directory and files
mkdir -p /var/log/comfyui
LOG_FILE="/var/log/comfyui/install.log"
CLOUD_INIT_LOG="/var/log/cloud-init-output.log"

# Log all output to both our custom log and cloud-init log
exec > >(tee -a "$LOG_FILE" "$CLOUD_INIT_LOG") 2>&1

echo "=== ComfyUI Installation Started at $(date) ==="
echo "Installation log: $LOG_FILE"
echo "Cloud-init log: $CLOUD_INIT_LOG"

# Update system
apt-get update
apt-get upgrade -y

# Install essential packages
apt-get install -y \
    git \
    python3.12 \
    python3.12-venv \
    python3.12-dev \
    python3-pip \
    wget \
    curl \
    unzip \
    build-essential \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Install NVIDIA drivers and CUDA (for g5.2xlarge instances)
echo "Installing NVIDIA drivers and CUDA..."

# Add NVIDIA package repositories
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.1-1_all.deb
dpkg -i cuda-keyring_1.1-1_all.deb
apt-get update

# Install CUDA toolkit
apt-get install -y cuda-toolkit-12-4

# Install NVIDIA driver
apt-get install -y nvidia-driver-535

# Create comfyui user
useradd -m -s /bin/bash comfyui
usermod -aG sudo comfyui

# Set up ComfyUI directory
COMFYUI_DIR="/home/<USER>/ComfyUI"
mkdir -p $COMFYUI_DIR
chown comfyui:comfyui /home/<USER>

# Switch to comfyui user for installation
sudo -u comfyui bash << 'EOF'
cd /home/<USER>

# Clone ComfyUI repository
git clone https://github.com/comfyanonymous/ComfyUI.git
cd ComfyUI

# Create Python virtual environment
python3.12 -m venv venv
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install PyTorch with CUDA support (for NVIDIA GPUs)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Install ComfyUI requirements
pip install -r requirements.txt

# Create model directories
mkdir -p models/checkpoints
mkdir -p models/vae
mkdir -p models/loras
mkdir -p models/controlnet
mkdir -p models/clip_vision
mkdir -p models/configs
mkdir -p models/embeddings
mkdir -p models/hypernetworks
mkdir -p models/upscale_models

# Create a startup script
cat > /home/<USER>/start_comfyui.sh << 'SCRIPT'
#!/bin/bash
cd /home/<USER>/ComfyUI
source venv/bin/activate
python main.py --listen 0.0.0.0 --port 8188
SCRIPT

chmod +x /home/<USER>/start_comfyui.sh

EOF

# Create systemd service for ComfyUI
cat > /etc/systemd/system/comfyui.service << 'SERVICE'
[Unit]
Description=ComfyUI Service
After=network.target

[Service]
Type=simple
User=comfyui
WorkingDirectory=/home/<USER>/ComfyUI
Environment=PATH=/home/<USER>/ComfyUI/venv/bin
ExecStart=/home/<USER>/ComfyUI/venv/bin/python main.py --listen 0.0.0.0 --port 8188
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
SERVICE

# Enable and start ComfyUI service
systemctl daemon-reload
systemctl enable comfyui.service

# Install AWS SSM Agent for remote management
snap install amazon-ssm-agent --classic
systemctl start snap.amazon-ssm-agent.amazon-ssm-agent.service
systemctl enable snap.amazon-ssm-agent.amazon-ssm-agent.service

# Set up log rotation for ComfyUI
cat > /etc/logrotate.d/comfyui << 'LOGROTATE'
/var/log/comfyui/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    copytruncate
}
LOGROTATE

echo "=== ComfyUI installation completed at $(date) ==="
echo "ComfyUI will be available on port 8188 after reboot"
echo "Use 'sudo systemctl status comfyui' to check service status"
echo "Use 'sudo systemctl start comfyui' to start the service manually"
echo ""
echo "=== Log Files for Troubleshooting ==="
echo "Installation log: $LOG_FILE"
echo "Cloud-init log: $CLOUD_INIT_LOG"
echo "Service logs: sudo journalctl -u comfyui"
echo ""

# Create a status file to indicate installation completion
echo "ComfyUI installation completed at $(date)" > /var/log/comfyui/install-complete.flag

# Reboot to ensure NVIDIA drivers are properly loaded
echo "=== Rebooting system to complete installation ==="
reboot
