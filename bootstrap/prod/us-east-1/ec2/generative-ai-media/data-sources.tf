data "terraform_remote_state" "production_vpc1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "shared_vars" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/shared-vars/terraform.tfstate"
    region = "us-east-1"
  }
}

# Try to get Ubuntu 24.04 LTS AMI first
data "aws_ami" "ubuntu_24" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = [
      "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-amd64-server-*",
      "ubuntu/images/hvm-ssd/ubuntu-noble-24.04-amd64-server-*"
    ]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "state"
    values = ["available"]
  }
}

# Fallback to Ubuntu 22.04 LTS AMI if 24.04 is not available
data "aws_ami" "ubuntu_22" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "state"
    values = ["available"]
  }
}
