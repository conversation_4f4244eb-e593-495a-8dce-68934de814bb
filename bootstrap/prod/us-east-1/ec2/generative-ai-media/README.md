# ComfyUI EC2 Instance

This Terraform configuration creates a g5.2xlarge EC2 instance with ComfyUI pre-installed for AI image generation.

## Instance Specifications

- **Instance Type**: g5.2xlarge (8 vCPUs, 32 GB RAM, 1x NVIDIA A10G GPU)
- **Operating System**: Ubuntu 22.04 LTS
- **Storage**: 200 GB GP3 EBS volume
- **Network**: VPC with Elastic IP

## ComfyUI Installation

The instance is automatically configured with:

- **ComfyUI**: Latest version from GitHub
- **Python**: 3.12 with virtual environment
- **PyTorch**: CUDA-enabled version for GPU acceleration
- **NVIDIA Drivers**: CUDA 12.4 toolkit and drivers
- **System Service**: ComfyUI runs as a systemd service

## Access

### Web Interface
- **URL**: `http://<ELASTIC_IP>:8188`
- **Port**: 8188 (configured in security group)

### SSH Access
- **Port**: 22 (restricted to VPC CIDR: **********/12)
- **Key**: `gen-ai-media-production`
- **User**: `ubuntu` (default) or `comfyui` (for ComfyUI-specific tasks)

## Security Groups

The instance allows inbound traffic on:
- Port 22 (SSH) - from **********/12
- Port 80 (HTTP) - from anywhere
- Port 3000 - from anywhere  
- Port 8188 (ComfyUI) - from anywhere

## Service Management

### Check ComfyUI Status
```bash
sudo systemctl status comfyui
```

### Start/Stop ComfyUI
```bash
sudo systemctl start comfyui
sudo systemctl stop comfyui
sudo systemctl restart comfyui
```

### View Logs
```bash
# Installation logs
sudo tail -f /var/log/comfyui-install.log

# Service logs
sudo journalctl -u comfyui -f
```

## Model Management

ComfyUI models are stored in `/home/<USER>/ComfyUI/models/`:

- `checkpoints/` - Stable Diffusion models (.ckpt, .safetensors)
- `vae/` - VAE models
- `loras/` - LoRA models
- `controlnet/` - ControlNet models
- `embeddings/` - Textual inversions
- `upscale_models/` - Upscaling models

### Adding Models

1. SSH into the instance
2. Switch to comfyui user: `sudo su - comfyui`
3. Download models to appropriate directories
4. Restart ComfyUI if needed: `sudo systemctl restart comfyui`

## Troubleshooting

### GPU Not Detected
```bash
# Check NVIDIA driver installation
nvidia-smi

# Check CUDA installation
nvcc --version
```

### ComfyUI Not Starting
```bash
# Check service status
sudo systemctl status comfyui

# Check logs for errors
sudo journalctl -u comfyui --no-pager

# Manual start for debugging
sudo su - comfyui
cd /home/<USER>/ComfyUI
source venv/bin/activate
python main.py --listen 0.0.0.0 --port 8188
```

### Installation Issues
Check the installation log:
```bash
sudo cat /var/log/comfyui-install.log
```

## Cost Considerations

- **g5.2xlarge**: ~$1.21/hour (on-demand pricing)
- **200 GB GP3 storage**: ~$20/month
- **Elastic IP**: Free when attached to running instance

## Deployment

```bash
# Initialize and plan
terraform init
terraform plan

# Apply changes
terraform apply

# Get the Elastic IP
terraform output
```

## Notes

- The instance will reboot once during initial setup to load NVIDIA drivers
- ComfyUI service starts automatically after reboot
- Installation takes approximately 10-15 minutes
- First ComfyUI startup may take additional time to initialize
