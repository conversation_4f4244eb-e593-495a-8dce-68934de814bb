locals {
  key_name = "gen-ai-media-production"
}

resource "aws_security_group" "gen-ai-media" {
  name        = "gen-ai-media-sg"
  description = "gen-ai-media security group"
  vpc_id      = data.terraform_remote_state.production_vpc1.outputs.vpc_id

  tags = {
    Name = "gen-ai-media sg"
  }
}

resource "aws_security_group_rule" "ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["**********/12"]
  security_group_id = aws_security_group.gen-ai-media.id
  description       = "Allow SSH inbound traffic from specified make.com IPs"
}

resource "aws_security_group_rule" "port_80" {
  type              = "ingress"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.gen-ai-media.id
  description       = "Allow inbound traffic 80"
}

resource "aws_security_group_rule" "port_3000" {
  type              = "ingress"
  from_port         = 3000
  to_port           = 3000
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.gen-ai-media.id
  description       = "Allow inbound traffic 3000"
}

resource "aws_security_group_rule" "allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.gen-ai-media.id
  cidr_blocks       = ["0.0.0.0/0"]
}

data "aws_security_group" "gen-ai-media" {
  name   = "gen-ai-media-sg"
  vpc_id = data.terraform_remote_state.production_vpc1.outputs.vpc_id
}

resource "aws_instance" "gen-ai-media" {
  ami                         = "ami-05ffe3c48a9991133"
  instance_type               = "t3.micro"
  subnet_id                   = data.terraform_remote_state.production_vpc1.outputs.public_subnet_ids[0]
  associate_public_ip_address = false
  key_name                    = local.key_name
  vpc_security_group_ids = [data.aws_security_group.gen-ai-media.id]
  lifecycle {
    ignore_changes = [vpc_security_group_ids]
  }

  root_block_device {
    volume_size = 15
    volume_type = "gp3"
  }

  tags = {
    Name = "gen-ai-media"
    team = "glow"
  }
}

resource "aws_eip" "gen-ai-media_eip" {
  instance = aws_instance.gen-ai-media.id
  domain   = "vpc"
  tags = {
    Name = "gen-ai-media"
  }
}
