locals {
  key_name = "gen-ai-media-production"
}

resource "aws_security_group" "gen-ai-media" {
  name        = "gen-ai-media-sg"
  description = "gen-ai-media security group"
  vpc_id      = data.terraform_remote_state.production_vpc1.outputs.vpc_id

  tags = {
    Name = "gen-ai-media sg"
  }
}

resource "aws_security_group_rule" "ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["**********/12"]
  security_group_id = aws_security_group.gen-ai-media.id
  description       = "Allow SSH inbound traffic from specified make.com IPs"
}

resource "aws_security_group_rule" "port_80" {
  type              = "ingress"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.gen-ai-media.id
  description       = "Allow inbound traffic 80"
}

resource "aws_security_group_rule" "port_8188" {
  type              = "ingress"
  from_port         = 8188
  to_port           = 8188
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.gen-ai-media.id
  description       = "Allow ComfyUI web interface access"
}

resource "aws_security_group_rule" "allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.gen-ai-media.id
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_instance" "gen-ai-media" {
  ami                         = data.aws_ami.ubuntu.id
  instance_type               = "g5.2xlarge"
  subnet_id                   = data.terraform_remote_state.production_vpc1.outputs.public_subnet_ids[0]
  associate_public_ip_address = false
  key_name                    = local.key_name
  vpc_security_group_ids      = [aws_security_group.gen-ai-media.id]
  user_data                   = file("${path.module}/user-data.sh")

  lifecycle {
    ignore_changes = [vpc_security_group_ids]
  }

  root_block_device {
    volume_size = 200
    volume_type = "gp3"
  }

  tags = {
    Name = "gen-ai-media-comfyui"
    team = "design"
    Purpose = "ComfyUI for AI image generation"
  }
}

resource "aws_eip" "gen-ai-media_eip" {
  instance = aws_instance.gen-ai-media.id
  domain   = "vpc"
  tags = {
    Name = "gen-ai-media-comfyui"
    Purpose = "ComfyUI for AI image generation"
  }
}
