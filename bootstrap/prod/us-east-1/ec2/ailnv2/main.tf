locals {
  key_name = "ailnv2-production"
}

resource "aws_security_group" "ailnv2" {
  name        = "ailnv2-sg"
  description = "ailnv2 security group"
  vpc_id      = data.terraform_remote_state.production_vpc1.outputs.vpc_id

  tags = {
    Name = "ailnv2 sg"
  }
}

resource "aws_security_group_rule" "ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["**********/12"]
  security_group_id = aws_security_group.ailnv2.id
  description       = "Allow SSH inbound traffic from specified make.com IPs"
}

resource "aws_security_group_rule" "port_80" {
  type              = "ingress"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ailnv2.id
  description       = "Allow inbound traffic 80"
}

resource "aws_security_group_rule" "port_3000" {
  type              = "ingress"
  from_port         = 3000
  to_port           = 3000
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ailnv2.id
  description       = "Allow inbound traffic 3000"
}

resource "aws_security_group_rule" "allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.ailnv2.id
  cidr_blocks       = ["0.0.0.0/0"]
}

data "aws_security_group" "ailnv2" {
  name   = "ailnv2-sg"
  vpc_id = data.terraform_remote_state.production_vpc1.outputs.vpc_id
}

resource "aws_instance" "ailnv2" {
  ami                         = "ami-05ffe3c48a9991133"
  instance_type               = "t3.micro"
  subnet_id                   = data.terraform_remote_state.production_vpc1.outputs.public_subnet_ids[0]
  associate_public_ip_address = false
  key_name                    = local.key_name
  vpc_security_group_ids = [data.aws_security_group.ailnv2.id]
  lifecycle {
    ignore_changes = [vpc_security_group_ids]
  }

  root_block_device {
    volume_size = 15
    volume_type = "gp3"
  }

  tags = {
    Name = "ailnv2"
    team = "glow"
  }
}

resource "aws_eip" "ailnv2_eip" {
  instance = aws_instance.ailnv2.id
  domain   = "vpc"
  tags = {
    Name = "ailnv2"
  }
}
