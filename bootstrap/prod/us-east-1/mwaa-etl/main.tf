module "subnets_info" {
  source          = "**************:luxurypresence/tf-modules.git//subnets-info?ref=main&depth=1"
  vpc_id          = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.database.id
  type            = "private"
}

module "mwaa" {
  source = "**************:luxurypresence/tf-modules.git//mwaa-cluster?ref=main&depth=1"
  environment                           = "production"
  cluster_name                          = "production-etl"
  airflow_version                       = "2.9.2"
  environment_class                     = "mw1.xlarge"
  min_workers                           = 3
  max_workers                           = 25
  dag_s3_path                           = "dags/"
  plugins_s3_path                       = "plugins/plugins.zip"
  requirements_s3_path                  = "requirements.txt"
  schedulers                            = 3
  source_bucket_prefix                  = "lp-data-mwaa"
  webserver_access_mode                 = "PUBLIC_ONLY"
  athena_data_access_buckets = [
    "arn:aws:s3:::lp-data-airflow-metadata-production",
    "arn:aws:s3:::lp-data-airflow-metadata-production/*",
    "arn:aws:s3:::lp-posthog-data",
    "arn:aws:s3:::lp-posthog-data/*"
  ]
  athena_query_results_buckets = [
    "arn:aws:s3:::aws-athena-query-results-*-us-east-1",
    "arn:aws:s3:::aws-athena-query-results-*-us-east-1/*",
    "arn:aws:s3:::data-etl-service-prod",
    "arn:aws:s3:::data-etl-service-prod/*"
  ]
  airflow_configuration_options = {
    "celery.worker_autoscale"             = "20,0"
    "webserver.web_server_master_timeout" = 180
    "webserver.web_server_worker_timeout" = 180
    "metrics.metrics_allow_list"          = "executor,pool,dagrun,scheduler"
    "scheduler.min_file_process_interval" = 600
    "webserver.expose_config"             = "True"
    "core.min_serialized_dag_update_interval" = 600
    "core.dag_file_processor_timeout"      = 600
    "scheduler.schedule_after_task_execution" = "false"
  }

  network_configuration = {
    vpc_id             = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.database.id
    security_group_ids = []
    subnet_ids = [
      module.subnets_info.subnets_ids[0],
      module.subnets_info.subnets_ids[1]
    ]
  }

  logging_configuration = {
    dag_processing_logs = {
      enable    = true
      log_level = "ERROR"
    }
    scheduler_logs = {
      enable    = true
      log_level = "INFO"
    }
    task_logs = {
      enable    = true
      log_level = "INFO"
    }
    webserver_logs = {
      enable    = true
      log_level = "ERROR"
    }
    worker_logs = {
      enable    = true
      log_level = "INFO"
    }
  }

  emr_configuration = {
    iam_role_arns = [data.terraform_remote_state.emr1.outputs.emr_iam_role_arn, data.terraform_remote_state.emr1.outputs.emr_ec2_iam_role_arn]
    security_group_id = data.terraform_remote_state.emr1.outputs.emr_security_group_id
  }

  data_sync_updates_topic_arn = data.terraform_remote_state.mwaa_shared1.outputs.data_sync_updates_topic_arn
  tags = {
    "Cluster" = "production-etl"
  }

  additional_mwaa_policies = [
    "arn:aws:iam::aws:policy/AdministratorAccess"
  ]
}
