data "terraform_remote_state" "emr1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/emr1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "mwaa_shared1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/mwaa-shared1/terraform.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "shared_vars" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/shared-vars/terraform.tfstate"
    region = "us-east-1"
  }
}
