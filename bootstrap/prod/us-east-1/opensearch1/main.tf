locals {
  legacy_cidr         = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr
  bastion_cidr        = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr
  production_cidr     = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.production.cidr
  databasen_cidr      = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.database.cidr
  ops_cidr            = data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.cidr
  database_vpc_id     = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.database.id
}

data "aws_subnets" "database_subnets" {
  filter {
    name   = "vpc-id"
    values = [local.database_vpc_id]
  }

  tags = {
    Type = "database"
  }
}

module "search_cluster" {
  source = "**************:luxurypresence/tf-modules.git//opensearch?ref=v1.0.0"
  name           = "search-service"
  cluster_name   = "search-service-cluster"
  environment    = "production"
  instance_class = "r7g.2xlarge.search"
  engine = {
    engine_version = "OpenSearch_2.17"
  }
  vpc = {
    vpc_id          = local.database_vpc_id
    cluster_subnets = slice(data.aws_subnets.database_subnets.ids, 0, 3)
    allowed_cidr_blocks = [
      local.legacy_cidr,
      local.ops_cidr,
      local.bastion_cidr,
      local.production_cidr,
      local.databasen_cidr
    ]
  }

  enable_advanced_security   = true
  enabled_internal_master    = true
  allocated_storage          = 50
  ebs_throughput             = 500
  ebs_iops                   = 7000
  az_count                   = 3
  instance_count             = 3
  master_enabled             = true
  master_count               = 3
  master_type                = "c7g.large.search"
  custom_domain              = "search-service-opensearch-cluster.luxurypresence.com"
  custom_domain_tls_cert_arn = "arn:aws:acm:us-east-1:381475384502:certificate/333582ef-c9de-4fd8-b2b8-eddf299e21e0"
  create_manual_snapshot_resources = true
  tags = {
    env  = "production"
    team = "maps"
    app  = "search-service"
  }
}

