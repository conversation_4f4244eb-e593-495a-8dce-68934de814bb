data "terraform_remote_state" "shared_vars" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/shared-vars/terraform.tfstate"
    region = "us-east-1"
  }
}

data "aws_vpc" "db_vpc" {
  filter {
    name   = "tag:peering"
    values = ["production-db-vpc"]
  }
}


data "aws_subnets" "database_subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.db_vpc.id]
  }

  tags = {
    Type = "database"
  }
}
