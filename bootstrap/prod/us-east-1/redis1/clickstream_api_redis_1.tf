module "clickstream_api_redis_1" {
  source       = "**************:luxurypresence/tf-modules.git//redis?ref=main"
  environment  = "production"
  cluster_name = "clickstream-api-redis-1"
  name         = "clickstream-api-redis-1"
  port         = 6379
  engine = {
    engine_version = "7.1"
    family         = "redis7"
  }

  instance_class             = "cache.t4g.small"
  automatic_failover_enabled = false
  multi_az_enabled           = false
  tags = {
    app = "clickstream-api"
  }
  number_of_shards        = 1
  replicas_per_node_group = 1
  vpc = {
    vpc_id          = data.aws_vpc.db_vpc.id
    cluster_subnets = data.aws_subnets.database_subnets.ids
    allowed_cidr_blocks = [
      "172.31.0.0/16", # Legacy VPC
      "172.16.0.0/16", # Bastion VPC
      "10.20.0.0/16",  # Production VPC
      "10.3.0.0/16",   # Database VPC
    ]
  }
  maintenance = {
    maintenance_window        = "Sun:11:00-Sun:12:00"
    snapshot_window           = "01:00-03:00"
    snapshot_retention_period = 3
  }
}
