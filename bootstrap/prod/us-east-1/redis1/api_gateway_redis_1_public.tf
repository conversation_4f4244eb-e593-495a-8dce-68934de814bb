module "api_gateway_redis_1_public" {
  source       = "**************:luxurypresence/tf-modules.git//redis?ref=main"
  environment  = "production"
  cluster_name = "lp-api-gateway-public"
  name         = "api-gateway-redis-1-public"
  port         = 6379
  engine = {
    engine_version = "7.0"
    family         = "redis7"
  }

  instance_class             = "cache.t3.medium"
  automatic_failover_enabled = false
  multi_az_enabled           = false
  tags = {
    app = "api-gateway-redis-1-public"
  }
  number_of_shards        = 1
  replicas_per_node_group = 1
  vpc = {
    vpc_id          = data.aws_vpc.db_vpc.id
    cluster_subnets = data.aws_subnets.database_subnets.ids
    allowed_cidr_blocks = [
      "172.31.0.0/16", # Legacy VPC
      "172.16.0.0/16", # Bastion VPC
      "10.20.0.0/16",  # Production VPC
      "10.3.0.0/16",   # Database VPC
    ]
  }
  maintenance = {
    maintenance_window        = "Sun:11:00-Sun:12:00"
    snapshot_window           = "01:00-03:00"
    snapshot_retention_period = 3
  }
  parameters = [
    {
      name  = "maxmemory-policy"
      value = "allkeys-lru"
    }
  ]
}
