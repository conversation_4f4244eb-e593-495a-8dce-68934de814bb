module "public_api_gateway_redis_1" {
  source       = "**************:luxurypresence/tf-modules.git//redis?ref=main"
  environment  = "production"
  cluster_name = "lp"
  name         = "public-api-gateway-redis-1"
  port         = 6379
  engine = {
    engine_version = "7.0"
    family         = "redis7"
  }

  instance_class             = "cache.t3.medium"
  automatic_failover_enabled = false
  multi_az_enabled           = false
  tags = {
    app = "public-api-gateway-redis-1"
  }
  number_of_shards        = 1
  replicas_per_node_group = 1
  vpc = {
    vpc_id          = data.aws_vpc.db_vpc.id
    cluster_subnets = data.aws_subnets.database_subnets.ids
    allowed_cidr_blocks = [
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.database.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.production.cidr
    ]
  }
  maintenance = {
    maintenance_window        = "Sun:11:00-Sun:12:00"
    snapshot_window           = "01:00-03:00"
    snapshot_retention_period = 3
  }
}
