data "aws_vpc" "prod_vpc" {
  filter {
    name   = "tag:peering"
    values = ["production_vpc1"]
  }
}

data "aws_vpc" "db_vpc" {
  filter {
    name   = "tag:peering"
    values = ["production-db-vpc"]
  }
}


module "prod_vpc_peering1" {
  source = "**************:luxurypresence/tf-modules.git//vpc-peering?ref=main&depth=1"
  providers = {
    aws.src = aws
    aws.dst = aws
  }

  source_vpc_id              = data.aws_vpc.prod_vpc.id
  destination_vpc_id         = data.aws_vpc.db_vpc.id
}
