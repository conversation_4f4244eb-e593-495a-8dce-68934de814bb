data "terraform_remote_state" "vpc1" {
  backend = "s3"
  config = {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/vpc1/terraform.tfstate"
    region = "us-east-1"
  }
}

module "emr" {
  source = "**************:luxurypresence/tf-modules.git//emr?ref=main&depth=1"
  environment_name        = "production"
  emr_bucket_prefix      = "lp-data-emr"
  emr_access_bucket_arns = [
    "arn:aws:s3:::lp-data-emr-production",
    "arn:aws:s3:::lp-data-emr-production/*",
    "arn:aws:s3:::lp-datalakehouse-production",
    "arn:aws:s3:::lp-datalakehouse-production/*",
    "arn:aws:s3:::lp-data-mwaa-production",
    "arn:aws:s3:::lp-data-mwaa-production/*",
    "arn:aws:s3:::qa-extract-s3-bucket-production",
    "arn:aws:s3:::qa-extract-s3-bucket-production/*",
    "arn:aws:s3:::lp-datawarehouse-production",
    "arn:aws:s3:::lp-datawarehouse-production/*",
    "arn:aws:s3:::lp-posthog-data",
    "arn:aws:s3:::lp-posthog-data/*",
    "arn:aws:s3:::etl-slug-*",
    "arn:aws:s3:::etl-slug-*/*"
  ]
  network_configuration = {
    vpc_id     = data.terraform_remote_state.vpc1.outputs.vpc_id
    cidr_block = data.terraform_remote_state.vpc1.outputs.vpc_cidr_block
  }
  additional_security_group_rules = [
    {
      type        = "ingress"
      from_port   = -1
      to_port     = -1
      protocol    = -1
      cidr_blocks = ["172.16.0.0/16"]  # bastion VPC
    },
    {
      type        = "ingress"
      from_port   = -1
      to_port     = -1
      protocol    = -1
      cidr_blocks = ["172.31.0.0/16"] # legacy VPC
    },
    {
      type        = "ingress"
      from_port   = -1
      to_port     = -1
      protocol    = -1
      cidr_blocks = ["10.3.0.0/16"] # DB VPC
    }
  ]
  tags = {
    Cluster = "production"
  }
}
