locals {
  vpc_id              = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.database.id
  ops_cidr            = data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.cidr
  legacy_cidr         = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr
  bastion_cidr        = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr
  new_production_cidr = data.terraform_remote_state.networking_self.outputs.vpc_cidr_block
  master_user_secret_policy_principals = [
    data.aws_iam_session_context.example.issuer_arn,
    "arn:aws:iam::381475384502:user/aborges",
    "arn:aws:iam::381475384502:user/brad.cook",
    "arn:aws:iam::381475384502:user/qrobinson",
    "arn:aws:iam::381475384502:user/wilson.heitor",
    "arn:aws:iam::381475384502:user/ghardt"
  ]
}
