module "mls_database" {
  source                      = "**************:luxurypresence/tf-modules.git//rds-aurora?ref=main&depth=1"
  name                        = "mls-cluster-2"
  environment                 = "production"
  username                    = "mls_search"
  port                        = 5432
  subnet_type                 = "database"
  enable_performance_insights = true
  master_user_secret_policy_principals = local.master_user_secret_policy_principals
  iam_database_authentication_enabled = true
  storage_type = "aurora-iopt1"
  vpc = {
    vpc_id                         = local.vpc_id
    allowed_self_vpc_subnets_types = ["private", "database", "public"] # Allow to communicate to DB from private and database subnets in the same VPC
    allowed_external_cidr_blocks = [
      local.legacy_cidr,
      local.ops_cidr,
      local.bastion_cidr,
      local.new_production_cidr
    ]
  }
  engine = {
    engine         = "aurora-postgresql"
    engine_version = "16.6"
    family         = "aurora-postgresql16"
  }
  instances = {
    1 : {
      instance_class : "db.r6g.xlarge"
    },
    2 : {
      instance_class : "db.r6g.xlarge"
    },
    3 : {
      instance_class : "db.r6g.xlarge"
    },
    glow-ro : {
      instance_class : "db.r6g.xlarge"
    },
    metabase-ro : {
      instance_class : "db.r6g.xlarge"
    }
  }
  endpoints = {}
  cluster_parameters = [
    {
      name         = "log_connections"
      value        = "1"
      apply_method = "pending-reboot"
    },
    {
      name         = "rds.logical_replication"
      value        = 1
      apply_method = "pending-reboot"
    },
    {
      name         = "shared_preload_libraries"
      value        = "pg_stat_statements,pglogical"
      apply_method = "pending-reboot"
    },
    {
      name         = "max_replication_slots"
      value        = 25000
      apply_method = "pending-reboot"
    },
    {
      name         = "max_worker_processes",
      value        = 25000
      apply_method = "pending-reboot"
    },
    {
      name         = "max_wal_senders",
      value        = 25000
      apply_method = "pending-reboot"
    },
    {
      name         = "max_logical_replication_workers",
      value        = 25000
      apply_method = "pending-reboot"
    },
    {
      name         = "max_connections"
      value        = 30000
      apply_method = "pending-reboot"
    }
  ]
  parameters = [
    {
      name         = "shared_preload_libraries"
      value        = "pg_stat_statements,pglogical"
      apply_method = "pending-reboot"
    }
  ]
}
