# No interpolations allowed in Backend configurations
terraform {
  backend "s3" {
    bucket = "lp-tf-state-prod"
    key    = "us-east-1/eks1_karpenter/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn = "arn:aws:iam::381475384502:role/atlantis-access-production"
  }
}

provider "kubernetes" {
  host                   = module.production_eks1.cluster_endpoint
  cluster_ca_certificate = base64decode(module.production_eks1.cluster_certificate_authority_data)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args = ["eks", "get-token", "--cluster-name", module.production_eks1.cluster_name, "--region", "us-east-1"]
  }
}

provider "helm" {
  kubernetes {
    host                   = module.production_eks1.cluster_endpoint
    cluster_ca_certificate = base64decode(module.production_eks1.cluster_certificate_authority_data)
    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args = ["eks", "get-token", "--cluster-name", module.production_eks1.cluster_name, "--region", "us-east-1"]
    }
  }
}

provider "kubectl" {
  host                   = module.production_eks1.cluster_endpoint
  cluster_ca_certificate = base64decode(module.production_eks1.cluster_certificate_authority_data)
  load_config_file       = false
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args = ["eks", "get-token", "--cluster-name", module.production_eks1.cluster_name, "--region", "us-east-1"]
  }
}
