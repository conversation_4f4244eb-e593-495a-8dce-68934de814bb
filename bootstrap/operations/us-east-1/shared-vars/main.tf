locals {
  # Operations Account
  operations_account = {
    vpc_config = {
      cidr = "10.254.0.0/16",
      id = "vpc-053f124920ca76e61"
    }
  }

  # Production Account
  production_account = {
    vpc_config = {
      legacy = {
        cidr = "172.31.0.0/16"
      }
      bastion = {
        cidr = "172.16.0.0/16"
      }
      production = {
        cidr = "10.20.0.0/16",
        id = "vpc-0b25911da3ca9c218"
      }
      db = {
        cidr = "10.3.0.0/16",
        id = "vpc-0234fd6295cb753fc"
      }
    }
  }

  # Staging Account
  staging_account = {
    vpc_config = {
      legacy = {
        cidr = "10.1.0.0/16"
      }
      staging = {
        cidr = "10.21.0.0/16",
        id = "vpc-06e4165b4af3f1edc"
      }
    }
  }
}
