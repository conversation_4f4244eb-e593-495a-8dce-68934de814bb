module "operations_database_1" {
  source      = "**************:luxurypresence/tf-modules.git//rds-aurora?ref=main&depth=1"
  name        = "operations-cluster-1"
  environment = "production"
  username    = "luxurypresenceoperations82"
  port        = 5432
  subnet_type = "private"
  enable_performance_insights = true
  vpc = {
    vpc_id                         =       data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.id
    allowed_self_vpc_subnets_types = ["private"] # Allow to communicate to DB from private subnets in the same VPC
    allowed_external_cidr_blocks = [
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.production.cidr,
      data.terraform_remote_state.shared_vars.outputs.production.vpc_config.bastion.cidr,
      data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.cidr,
      data.terraform_remote_state.shared_vars.outputs.staging.vpc_config.legacy.cidr,
      data.terraform_remote_state.shared_vars.outputs.staging.vpc_config.staging.cidr,
    ]
  }
  security_group_egress_rules = {
    to_cidrs = {
      cidr_blocks = ["0.0.0.0/0"]
    }
  }
  engine = {
    engine         = "aurora-postgresql"
    engine_version = "16.4"
    family         = "aurora-postgresql16"
  }
  instances = {
    1 : {
      instance_class : "db.t4g.medium"
    }
  }
  cluster_parameters = [
    {
      name  = "log_connections"
      value = "1"
    }, {
      name  = "rds.logical_replication"
      value = "1",
      apply_method = "pending-reboot"
    }, {
      name  = "shared_preload_libraries"
      value = "pg_stat_statements,pglogical"
      apply_method = "pending-reboot"
    }, {
      name  = "max_replication_slots"
      value = "30"
      apply_method = "pending-reboot"
    }, {
      name = "max_worker_processes",
      value = "30"
      apply_method = "pending-reboot"
    },
    {
      name = "max_wal_senders",
      value = "30"
      apply_method = "pending-reboot"
    }
  ]
}
