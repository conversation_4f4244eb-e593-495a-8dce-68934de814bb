module "crossplane-provider-aws-production-eks1" {
  source = "**************:luxurypresence/tf-modules.git//iam/irsa?ref=main&depth=1"

  role_name = "argocd-notifications-controller-role"
  policy_name = "argocd-notifications-controller-policy"
  environment = "operations"
  cluster_oidc_provider_arn = "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/D56EFAA331D04529FA7B09A983F01E31"
  namespace_service_accounts = [
    "argocd:argocd-notifications-controller",
  ]
  policy_manifest_file = "./policies/argocd-provider-aws.json"
  condition_operator = "StringLike"
}
