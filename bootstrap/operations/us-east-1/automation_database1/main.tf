module "automation_database_1" {
  source      = "**************:luxurypresence/tf-modules.git//rds-aurora?ref=main&depth=1"
  name        = "automation-cluster-1"
  environment = "production"
  username    = "luxurypresenceautomation82"
  port        = 5432
  subnet_type = "private"
  enable_performance_insights = true
  publicly_accessible = true
  master_user_secret_policy_principals = local.master_user_secret_policy_principals
  vpc = {
    vpc_id                         =       data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.id
    allowed_self_vpc_subnets_types = ["public"]
    allowed_external_cidr_blocks = var.ips_blocks
  }
  security_group_egress_rules = {
    to_cidrs = {
      cidr_blocks = ["0.0.0.0/0"]
    }
  }
  engine = {
    engine         = "aurora-postgresql"
    engine_version = "16.4"
    family         = "aurora-postgresql16"
  }
  instances = {
    1 : {
      instance_class : "db.t4g.medium"
    }
  }
}
