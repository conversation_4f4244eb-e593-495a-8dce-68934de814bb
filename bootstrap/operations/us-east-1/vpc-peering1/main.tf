module "operations_vpc_staging1" {
  source = "**************:luxurypresence/tf-modules.git//vpc-peering?ref=main&depth=1"
  providers = {
    aws.src = aws.src
    aws.dst = aws.staging
  }

  source_vpc_id              = data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.id
  destination_vpc_id         = data.terraform_remote_state.shared_vars.outputs.staging.vpc_config.staging.id
  auto_accept                = false
  auto_accept_from_accepter  = true
}


module "operations_vpc_production_db1" {
  source = "**************:luxurypresence/tf-modules.git//vpc-peering?ref=main&depth=1"
  providers = {
    aws.src = aws.src
    aws.dst = aws.production
  }

  source_vpc_id              = data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.id
  destination_vpc_id         = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.db.id
  auto_accept                = false
  auto_accept_from_accepter  = true
}


module "operations_vpc_production1" {
  source = "**************:luxurypresence/tf-modules.git//vpc-peering?ref=main&depth=1"
  providers = {
    aws.src = aws.src
    aws.dst = aws.production
  }

  source_vpc_id              = data.terraform_remote_state.shared_vars.outputs.operations.vpc_config.id
  destination_vpc_id         = data.terraform_remote_state.shared_vars.outputs.production.vpc_config.production.id
  auto_accept                = false
  auto_accept_from_accepter  = true
}
