# No interpolations allowed in Backend configurations
terraform {
  backend "s3" {
    bucket = "lp-tf-state-operations"
    key    = "us-east-1/vpc-peering1/terraform.tfstate"
    region = "us-east-1"
  }
}


provider "aws" {
  region = "us-east-1"
  alias = "src"
  assume_role {
    role_arn = "arn:aws:iam::172158540696:role/atlantis-access-operations"
  }
}

provider "aws" {
  region = "us-east-1"
  alias = "staging"
  assume_role {
    role_arn = "arn:aws:iam::093949242303:role/atlantis-access-staging"
  }
}


provider "aws" {
  region = "us-east-1"
  alias = "production"
  assume_role {
    role_arn = "arn:aws:iam::381475384502:role/atlantis-access-production"
  }
}
